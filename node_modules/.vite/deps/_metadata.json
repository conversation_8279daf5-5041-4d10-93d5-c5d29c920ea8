{"hash": "46c21b79", "configHash": "7e9a3446", "lockfileHash": "f29da481", "browserHash": "67103d21", "optimized": {"vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "157bc799", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "f1b0b629", "needsInterop": false}, "pinia-plugin-persistedstate": {"src": "../../pinia-plugin-persistedstate/dist/index.js", "file": "pinia-plugin-persistedstate.js", "fileHash": "eede63e1", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "2a4de380", "needsInterop": false}, "@heroicons/vue/24/outline": {"src": "../../@heroicons/vue/24/outline/esm/index.js", "file": "@heroicons_vue_24_outline.js", "fileHash": "9446cb3e", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "ec011d98", "needsInterop": false}, "crypto-js": {"src": "../../crypto-js/index.js", "file": "crypto-js.js", "fileHash": "6c380ea8", "needsInterop": true}}, "chunks": {"chunk-ZY5X6FX7": {"file": "chunk-ZY5X6FX7.js"}, "chunk-5FUTL2UF": {"file": "chunk-5FUTL2UF.js"}}}