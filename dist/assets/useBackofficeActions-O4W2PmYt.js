import{a as v,r,n as C}from"./index-DOaBqVmr.js";function O(){const d=C(),n=v({}),w=r(!1),s=r(1),h=r(10),g=r(0),c=r(0),u=r(""),i=r(!1),m=r("Select Client"),l=r(""),a=v({client_id:"",offset:0,limit:10,status:"",search:""}),D=e=>e?e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,","):"0",y=e=>e?e.charAt(0).toUpperCase()+e.slice(1):"",S=e=>{Object.keys(n).forEach(t=>{const o=parseInt(t);n[o]=o===e?!n[o]:!1})},p=()=>{Object.keys(n).forEach(e=>{n[parseInt(e)]=!1})};return{showDropdown:n,loading:w,currentPage:s,pageSize:h,totalRecords:g,offset:c,searchQuery:u,searchDropdown:i,searchDropdownPlaceholder:m,searchClient:l,moreParams:a,formatCurrency:D,capitalizeFirstLetter:y,toggleDropdown:S,closeDropdown:p,goToPage:(e,t)=>{s.value=e,c.value=e,a.offset=e,t&&t()},setClientId:(e,t)=>{a.client_id=e.value,m.value=e.text,i.value=!1,l.value="",t&&t()},toggleSearchDropdown:()=>{i.value=!i.value},filterByStatus:(e,t,o="status")=>t==="All"||t===""?e:e.filter(f=>parseInt(f[o])===parseInt(t.toString())),editRow:async(e,t,o)=>{p(),o&&await o(e),await d.push({name:t})},viewRelatedData:async(e,t,o,f)=>{o&&await o(e);const P=f||{client_id:e.client_id};await d.push({name:t,params:P})},resetFilters:()=>{s.value=1,c.value=0,u.value="",l.value="",a.client_id="",a.offset=0,a.search="",a.status=""},updatePagination:(e,t=10)=>{s.value=e,h.value=t,c.value=e,a.offset=e,a.limit=t},handleSearch:(e,t)=>{u.value=e,a.search=e,s.value=1,c.value=0,a.offset=0,t&&t()},formatDate:e=>e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"",formatTime:e=>e?new Date(`1970-01-01T${e}`).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!0}):""}}export{O as u};
