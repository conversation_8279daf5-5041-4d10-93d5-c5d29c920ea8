import{d as q,r,a as G,x as f,p as Q,c as y,b as e,j as J,i as w,t as l,h,C as _,e as k,k as u,M as K,z as B,n as O,o as C}from"./index-DOaBqVmr.js";import{_ as X}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";const Y={class:"space-y-6"},ee={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},te={class:"flex items-center justify-between"},se={class:"flex space-x-3"},oe=["disabled"],ne={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ie={key:1,class:"-ml-1 mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},re={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},le={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},ae={class:"flex items-center"},de={class:"ml-4"},ce={class:"text-2xl font-semibold text-gray-900"},ue={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},ve={class:"flex items-center"},me={class:"ml-4"},ge={class:"text-2xl font-semibold text-gray-900"},pe={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},fe={class:"flex items-center"},he={class:"ml-4"},_e={class:"text-2xl font-semibold text-gray-900"},xe={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},be={class:"flex items-center"},ye={class:"ml-4"},we={class:"text-2xl font-semibold text-gray-900"},ke={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},Ce={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Se={class:"flex space-x-2"},Ve={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},Me={class:"flex items-center"},je={class:"flex-1 bg-gray-200 rounded-full h-2 mr-2"},Ae={class:"text-sm font-medium text-gray-900"},Te={class:"text-gray-900 font-medium"},Be={class:"flex items-center space-x-2"},ze=["onClick"],Ze=["onClick"],Ee=["onClick"],Pe=q({__name:"Services",setup(Le){O();const a=r(!1),d=r([]),v=r(1),S=r(0),x=r(10),b=r(""),g=r(""),V=r("asc"),z=r(!1),i=G({service_status:"",service_category:"",environment:"",service_version:""}),Z=[{service_id:"SVC001",service_name:"Authentication API",service_category:"core",service_status:"healthy",environment:"production",service_version:"v2.1",endpoint_url:"https://api.mossbets.com/auth",uptime_percentage:99.9,avg_response_time:45,last_deployment:"2024-01-25T10:30:00Z",last_health_check:"2024-01-31T09:15:00Z"},{service_id:"SVC002",service_name:"Betting Engine",service_category:"core",service_status:"healthy",environment:"production",service_version:"v2.0",endpoint_url:"https://api.mossbets.com/betting",uptime_percentage:99.7,avg_response_time:120,last_deployment:"2024-01-20T14:45:00Z",last_health_check:"2024-01-31T09:14:00Z"},{service_id:"SVC003",service_name:"Payment Gateway",service_category:"integration",service_status:"warning",environment:"production",service_version:"v1.1",endpoint_url:"https://api.mossbets.com/payments",uptime_percentage:97.5,avg_response_time:250,last_deployment:"2024-01-15T08:20:00Z",last_health_check:"2024-01-31T09:13:00Z"},{service_id:"SVC004",service_name:"Analytics Service",service_category:"analytics",service_status:"healthy",environment:"production",service_version:"v1.0",endpoint_url:"https://api.mossbets.com/analytics",uptime_percentage:99.2,avg_response_time:180,last_deployment:"2024-01-10T16:30:00Z",last_health_check:"2024-01-31T09:12:00Z"},{service_id:"SVC005",service_name:"Notification Service",service_category:"integration",service_status:"critical",environment:"production",service_version:"v1.1",endpoint_url:"https://api.mossbets.com/notifications",uptime_percentage:92.1,avg_response_time:500,last_deployment:"2024-01-28T12:00:00Z",last_health_check:"2024-01-31T09:11:00Z"}],E=f(()=>d.value.length),L=f(()=>d.value.filter(s=>s.service_status==="healthy").length),D=f(()=>d.value.filter(s=>s.service_status==="warning").length),H=f(()=>d.value.filter(s=>s.service_status==="critical").length),c=async()=>{a.value=!0;try{await new Promise(n=>setTimeout(n,1e3));let s=[...Z];if(i.service_status&&(s=s.filter(n=>n.service_status===i.service_status)),i.service_category&&(s=s.filter(n=>n.service_category===i.service_category)),i.environment&&(s=s.filter(n=>n.environment===i.environment)),i.service_version&&(s=s.filter(n=>n.service_version===i.service_version)),b.value){const n=b.value.toLowerCase();s=s.filter(m=>m.service_name.toLowerCase().includes(n)||m.service_id.toLowerCase().includes(n)||m.endpoint_url.toLowerCase().includes(n))}g.value&&s.sort((n,m)=>{const j=n[g.value],A=m[g.value],T=V.value==="asc"?1:-1;return j<A?-1*T:j>A?1*T:0}),S.value=s.length;const t=(v.value-1)*x.value,o=t+x.value;d.value=s.slice(t,o)}catch(s){console.error("Error loading services:",s)}finally{a.value=!1}},P=()=>{c()},R=s=>{v.value=s,c()},U=s=>{b.value=s,v.value=1,c()},$=(s,t)=>{g.value=s,V.value=t,c()},N=s=>{M(s)},p=()=>{v.value=1,c()},M=s=>{console.log("View service:",s)},F=s=>{console.log("Edit service:",s)},I=s=>{console.log("View logs for service:",s)},W=()=>{console.log("Export services data")};return Q(()=>{c()}),(s,t)=>(C(),y("div",Y,[e("div",ee,[e("div",te,[t[8]||(t[8]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Services"),e("p",{class:"mt-1 text-sm text-gray-600"}," Manage and monitor all platform services and integrations ")],-1)),e("div",se,[e("button",{onClick:P,disabled:a.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"},[a.value?(C(),y("svg",ne,t[5]||(t[5]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):(C(),y("svg",ie,t[6]||(t[6]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))),w(" "+l(a.value?"Refreshing...":"Refresh"),1)],8,oe),e("button",{onClick:W,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},t[7]||(t[7]=[e("svg",{class:"-ml-1 mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),w(" Export ")]))])])]),e("div",re,[e("div",le,[e("div",ae,[t[10]||(t[10]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-100 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",de,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-500"},"Healthy Services",-1)),e("p",ce,l(L.value),1)])])]),e("div",ue,[e("div",ve,[t[12]||(t[12]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])])],-1)),e("div",me,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-500"},"Warning Services",-1)),e("p",ge,l(D.value),1)])])]),e("div",pe,[e("div",fe,[t[14]||(t[14]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-red-100 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",he,[t[13]||(t[13]=e("p",{class:"text-sm font-medium text-gray-500"},"Critical Services",-1)),e("p",_e,l(H.value),1)])])]),e("div",xe,[e("div",be,[t[16]||(t[16]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])])],-1)),e("div",ye,[t[15]||(t[15]=e("p",{class:"text-sm font-medium text-gray-500"},"Total Services",-1)),e("p",we,l(E.value),1)])])])]),e("div",ke,[e("div",Ce,[e("div",null,[t[18]||(t[18]=e("label",{for:"status-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Service Status",-1)),h(e("select",{id:"status-filter","onUpdate:modelValue":t[0]||(t[0]=o=>i.service_status=o),onChange:p,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},t[17]||(t[17]=[k('<option value="">All Statuses</option><option value="healthy">Healthy</option><option value="warning">Warning</option><option value="critical">Critical</option><option value="maintenance">Maintenance</option>',5)]),544),[[_,i.service_status]])]),e("div",null,[t[20]||(t[20]=e("label",{for:"category-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Category",-1)),h(e("select",{id:"category-filter","onUpdate:modelValue":t[1]||(t[1]=o=>i.service_category=o),onChange:p,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},t[19]||(t[19]=[k('<option value="">All Categories</option><option value="core">Core</option><option value="integration">Integration</option><option value="analytics">Analytics</option><option value="monitoring">Monitoring</option>',5)]),544),[[_,i.service_category]])]),e("div",null,[t[22]||(t[22]=e("label",{for:"environment-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Environment",-1)),h(e("select",{id:"environment-filter","onUpdate:modelValue":t[2]||(t[2]=o=>i.environment=o),onChange:p,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},t[21]||(t[21]=[e("option",{value:""},"All Environments",-1),e("option",{value:"production"},"Production",-1),e("option",{value:"staging"},"Staging",-1),e("option",{value:"development"},"Development",-1)]),544),[[_,i.environment]])]),e("div",null,[t[24]||(t[24]=e("label",{for:"version-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Version",-1)),h(e("select",{id:"version-filter","onUpdate:modelValue":t[3]||(t[3]=o=>i.service_version=o),onChange:p,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},t[23]||(t[23]=[k('<option value="">All Versions</option><option value="v1.0">v1.0</option><option value="v1.1">v1.1</option><option value="v2.0">v2.0</option><option value="v2.1">v2.1</option>',5)]),544),[[_,i.service_version]])])])]),J(X,{data:d.value,loading:a.value,"current-page":v.value,"total-records":S.value,"page-size":x.value,title:"Platform Services","row-key":"service_id","has-actions":!0,onPageChange:R,onSearch:U,onSort:$,onRowClick:N},{"header-actions":u(()=>[e("div",Se,[e("button",{onClick:t[4]||(t[4]=o=>z.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},t[25]||(t[25]=[e("svg",{class:"-ml-0.5 mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),w(" Add Service ")]))])]),"cell-service_status":u(({value:o})=>[e("span",{class:B([{"bg-green-100 text-green-800":o==="healthy","bg-yellow-100 text-yellow-800":o==="warning","bg-red-100 text-red-800":o==="critical","bg-gray-100 text-gray-800":o==="maintenance"},"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},l(o?o.charAt(0).toUpperCase()+o.slice(1):"-"),3)]),"cell-service_category":u(({value:o})=>[e("span",Ve,l(o?o.charAt(0).toUpperCase()+o.slice(1):"-"),1)]),"cell-uptime_percentage":u(({value:o})=>[e("div",Me,[e("div",je,[e("div",{class:B(["h-2 rounded-full",{"bg-green-500":o>=99,"bg-yellow-500":o>=95&&o<99,"bg-red-500":o<95}]),style:K({width:`${o}%`})},null,6)]),e("span",Ae,l(o)+"%",1)])]),"cell-avg_response_time":u(({value:o})=>[e("span",Te,l(o)+"ms ",1)]),actions:u(({item:o})=>[e("div",Be,[e("button",{onClick:n=>M(o),class:"text-blue-600 hover:text-blue-900 text-sm font-medium transition-colors duration-200"}," View ",8,ze),e("button",{onClick:n=>F(o),class:"text-indigo-600 hover:text-indigo-900 text-sm font-medium transition-colors duration-200"}," Edit ",8,Ze),e("button",{onClick:n=>I(o),class:"text-green-600 hover:text-green-900 text-sm font-medium transition-colors duration-200"}," Logs ",8,Ee)])]),_:1},8,["data","loading","current-page","total-records","page-size"])]))}});export{Pe as default};
