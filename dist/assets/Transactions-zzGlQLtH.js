import{d as Y,u as Z,r as n,q as ee,w as N,p as te,c as i,b as s,j as C,i as D,f as V,t as l,y as A,h as d,C as se,F as U,m as R,v as m,k as f,n as oe,o as u}from"./index-DOaBqVmr.js";import{_ as ae}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{b as le}from"./billPaymentsApi-BYuF0XYT.js";import{c as ne}from"./clientsApi-DPSRLazl.js";import{r as re}from"./ArrowPathIcon-BCh5HUKO.js";import{r as ie}from"./ArrowDownTrayIcon-BWjpub36.js";const ue={class:"space-y-6"},de={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},ce={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},me={class:"text-2xl font-bold text-gray-900"},fe={key:0,class:"text-lg text-gray-600"},be={class:"mt-4 sm:mt-0 flex space-x-3"},ve={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},pe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},ge=["value"],xe={class:"block text-sm font-medium text-gray-700 mb-1"},ye={key:0,class:"relative"},_e=["placeholder"],he={key:0,class:"absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"},ke=["onClick"],we={class:"flex space-x-2"},Ce={class:"flex space-x-2"},De={class:"text-sm font-medium text-gray-900"},Ve={class:"text-sm"},Se={class:"font-medium text-gray-900"},Te={class:"text-gray-500"},Ee={class:"text-sm"},Fe={class:"font-medium text-gray-900"},ze={class:"text-gray-500 text-xs"},Ne={class:"text-sm font-medium text-gray-900"},Ae={class:"text-sm"},Ue={class:"font-medium text-gray-900"},Re={class:"text-gray-500 text-xs"},Pe={class:"text-sm text-gray-900"},Ie=Y({__name:"Transactions",setup(Be){oe();const p=ee(),S=Z(),k=n(!1),x=n([]),y=n([]),b=n(1),_=n(0),T=n(10),P=n(""),B=n(""),j=n("asc"),w=n(""),h=n(!1),g=n(""),v=n(""),E=[{text:"Organization Name",value:1},{text:"Loan Number",value:2},{text:"Email",value:3},{text:"Phone Number",value:4}],c=n(E[0]),a=n({client_id:S.selectedClientId||"",loan_number:p.query.loan_number||"",client_phone:"",client_email:"",start:"",end:"",amount:"",source:"",channel_name:"",reference_type_id:"",trxn_reference_id:""}),q=[{key:"transaction_id",label:"Trxn ID",sortable:!0},{key:"customer",label:"Customer",sortable:!1},{key:"transaction_type",label:"Trxn Type",sortable:!1},{key:"amount",label:"Amount",sortable:!0},{key:"description",label:"Description",sortable:!1},{key:"created",label:"Date",sortable:!0}],r=async()=>{var o,t;k.value=!0;try{const e=await le.getTransactions({page:b.value,limit:T.value,...a.value});e.status===200?(x.value=((o=e.message)==null?void 0:o.data)||[],_.value=((t=e.message)==null?void 0:t.total_count)||0):(x.value=[],_.value=0)}catch(e){console.error("Error fetching transactions:",e),x.value=[],_.value=0}finally{k.value=!1}},F=async()=>{var o;try{const t=await ne.getClients({limit:100});if(t.status===200&&(y.value=(((o=t.message)==null?void 0:o.data)||[]).map(e=>({text:e.client_name,value:e.client_id})),p.params.client_id)){const e=y.value.find(z=>z.value===p.params.client_id);e&&(g.value=e.text,v.value=e.text)}}catch(t){console.error("Error fetching organizations:",t)}},M=o=>{b.value=o,r()},O=o=>{P.value=o,b.value=1,r()},$=(o,t)=>{B.value=o,j.value=t,r()},H=o=>{console.log("Transaction details:",o)},I=()=>{r(),F()},L=()=>{b.value=1,r()},Q=()=>{a.value={client_id:S.selectedClientId||"",loan_number:"",client_phone:"",client_email:"",start:"",end:"",amount:"",source:"",channel_name:"",reference_type_id:"",trxn_reference_id:""},g.value="",v.value="",b.value=1,r()},G=()=>{h.value=!h.value},J=o=>{a.value.client_id=o.value,g.value=o.text,v.value=o.text,h.value=!1,w.value="",r()},K=()=>{console.log("Export transactions data")},W=o=>(typeof o=="string"?parseFloat(o):o).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),X=o=>{if(!o)return"N/A";try{return new Date(o).toLocaleString()}catch{return"N/A"}};return N(()=>p.params.client_id,o=>{if(o){a.value.client_id=o;const t=y.value.find(e=>e.value===o);t&&(g.value=t.text,v.value=t.text),r()}}),N(()=>p.query.loan_number,o=>{o&&(a.value.loan_number=o,r())}),te(()=>{F(),r()}),(o,t)=>(u(),i("div",ue,[s("div",de,[s("div",ce,[s("div",null,[s("h1",me,[t[8]||(t[8]=D(" Transactions ")),v.value?(u(),i("span",fe,"- ("+l(v.value)+")",1)):V("",!0)]),t[9]||(t[9]=s("p",{class:"text-gray-600 mt-1"},"View and manage all financial transactions",-1))]),s("div",be,[s("button",{onClick:I,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[C(A(re),{class:"w-4 h-4 mr-2"}),t[10]||(t[10]=D(" Refresh "))])])])]),s("div",ve,[s("div",pe,[s("div",null,[t[11]||(t[11]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Filter By",-1)),d(s("select",{"onUpdate:modelValue":t[0]||(t[0]=e=>c.value=e),class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},[(u(),i(U,null,R(E,e=>s("option",{key:e.value,value:e},l(e.text),9,ge)),64))],512),[[se,c.value]])]),s("div",null,[s("label",xe,l(c.value.text),1),c.value.value===1?(u(),i("div",ye,[d(s("input",{"onUpdate:modelValue":t[1]||(t[1]=e=>w.value=e),onClick:G,placeholder:g.value||"Search organization",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,8,_e),[[m,w.value]]),h.value?(u(),i("div",he,[(u(!0),i(U,null,R(y.value,e=>(u(),i("div",{key:e.value,onClick:z=>J(e),class:"px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"},l(e.text),9,ke))),128))])):V("",!0)])):c.value.value===2?d((u(),i("input",{key:1,"onUpdate:modelValue":t[2]||(t[2]=e=>a.value.loan_number=e),type:"text",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter loan number"},null,512)),[[m,a.value.loan_number]]):c.value.value===3?d((u(),i("input",{key:2,"onUpdate:modelValue":t[3]||(t[3]=e=>a.value.client_email=e),type:"email",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter email address"},null,512)),[[m,a.value.client_email]]):c.value.value===4?d((u(),i("input",{key:3,"onUpdate:modelValue":t[4]||(t[4]=e=>a.value.client_phone=e),type:"text",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter phone number"},null,512)),[[m,a.value.client_phone]]):V("",!0)]),s("div",null,[t[12]||(t[12]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Date Range",-1)),s("div",we,[d(s("input",{"onUpdate:modelValue":t[5]||(t[5]=e=>a.value.start=e),type:"date",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[m,a.value.start]]),d(s("input",{"onUpdate:modelValue":t[6]||(t[6]=e=>a.value.end=e),type:"date",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[m,a.value.end]])])]),s("div",null,[t[13]||(t[13]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Amount",-1)),d(s("input",{"onUpdate:modelValue":t[7]||(t[7]=e=>a.value.amount=e),type:"number",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter amount"},null,512),[[m,a.value.amount]])])]),s("div",{class:"mt-4 flex justify-end space-x-3"},[s("button",{onClick:Q,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Clear "),s("button",{onClick:L,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Apply Filters ")])]),C(ae,{data:x.value,headers:q,loading:k.value,"current-page":b.value,"total-records":_.value,"page-size":T.value,title:"Transaction History","row-key":"transaction_id","has-actions":!1,onPageChange:M,onSearch:O,onSort:$,onRowClick:H},{"header-actions":f(()=>[s("div",Ce,[s("button",{onClick:K,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[C(A(ie),{class:"w-4 h-4 mr-2"}),t[14]||(t[14]=D(" Export "))])])]),"cell-transaction_id":f(({row:e})=>[s("div",De,l(e.transaction_id),1)]),"cell-customer":f(({row:e})=>[s("div",Ve,[s("div",Se,l(e.msisdn),1),s("div",Te,l(e.first_name)+" "+l(e.last_name),1)])]),"cell-transaction_type":f(({row:e})=>[s("div",Ee,[s("div",Fe,l(e.reference_name),1),s("div",ze,l(e.source),1)])]),"cell-amount":f(({row:e})=>[s("div",Ne,l(e.currency_code)+"."+l(W(e.amount)),1)]),"cell-description":f(({row:e})=>[s("div",Ae,[s("div",Ue,l(e.identifier_name)+" | "+l(e.reference_type)+" | "+l(e.channel_name),1),s("div",Re,l(e.description),1)])]),"cell-created":f(({row:e})=>[s("div",Pe,l(X(e.created)),1)]),_:1},8,["data","loading","current-page","total-records","page-size"])]))}});export{Ie as default};
