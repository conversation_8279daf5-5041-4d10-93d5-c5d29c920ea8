import{_ as b,c as h,b as s,j as m,B as k,f as x,i as u,k as l,l as f,t as d,u as C,o as c}from"./index-DOaBqVmr.js";import{s as g}from"./systemApi-CvVfCghC.js";import{_ as F}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{_ as S}from"./SearchFilter.vue_vue_type_script_setup_true_lang-jgd-6IIX.js";import"./MagnifyingGlassIcon-D7MUVaIi.js";import"./XMarkIcon-CqcEu60T.js";const U={components:{DataTable:F,SearchFilter:S},data(){return{searchClient:"",searchDropdown:!1,searchDropdownPlaceholder:"",clientName:"",organisations:[],data:[],roles:[],searchFilters:{search:"",status:"",role_id:"",start_date:"",end_date:"",client_id:""},fullPage:!0,total:0,offset:1,limit:100,showDropdown:[],isOpen:!1,isLoading:!1,tableHeaders:{user_name:"User",client_info:"Client Acc",contact_info:"Username",role_id:"Role",login_attempts:"Login Attempts",last_logged_on:"Last Login",status:"Status"},moreParams:{start:"",end:"",limit:100,page:1,status:"",client_id:""},time3:"",id:null,status:null}},setup(){return{authStore:C()}},watch:{searchClient(t,e){t!==e&&t!==""&&this.filterOrganizations()}},async mounted(){await this.setRoles(),this.authStore.isSuperUser||(this.moreParams.client_id=this.$route.params.client_id),await this.setUsers()},methods:{formatLastLoggedOn(t){if(!t)return"Never logged in";try{return new Date(t).toLocaleString()}catch{return"Never logged in"}},explodeByUnderscore(t){if(!t)return"";const e=t.split("_"),a=e[0]||"",n=e[1]||"",i=e[2]||"";return`${this.capitalizeFirstLetter(a)} ${this.capitalizeFirstLetter(n)} ${this.capitalizeFirstLetter(i)}`},capitalizeFirstLetter(t){return t.charAt(0).toUpperCase()+t.slice(1)},gotToPage(t){let e=this;e.moreParams.offset=t,e.offset=t,e.setUsers()},toggleDropdown(t){for(let e=0;e<this.showDropdown.length;e++)this.showDropdown[e]=e===t?!this.showDropdown[e]:!1},closeDropdown(){for(let t=0;t<this.showDropdown.length;t++)this.showDropdown[t]=!1},async resend(t){let e=this;const a={user_id:t};if(e.closeDropdown(),confirm("Are you sure you want to resend OTP to this user?"))try{e.isLoading=!0;const n=await g.resendOTP(a);n.status===200?alert("OTP sent successfully!"):alert("Error sending OTP: "+n.message)}catch(n){console.error("Error sending OTP:",n),alert("Error sending OTP")}finally{e.isLoading=!1}},async editRow(t){this.closeDropdown(),this.$router.push({name:"edit-user",params:{id:t.user_id}})},async setUsers(){var t,e;this.isLoading=!0;try{let a=await g.getUsers(this.moreParams);this.data=((t=a.message)==null?void 0:t.data)||[],this.total=parseInt(((e=a.message)==null?void 0:e.total_count)||0),this.showDropdown=[];for(let n=0;n<this.data.length;n++)this.showDropdown.push(!1)}catch(a){console.error("Error fetching users:",a),this.data=[],this.total=0}this.isLoading=!1},handleSearch(t){this.searchFilters.search=t,this.applyFilters()},handleFilter(t){Object.assign(this.searchFilters,t),this.applyFilters()},handleClearFilters(){this.searchFilters={search:"",status:"",role_id:"",start_date:"",end_date:"",client_id:""},this.applyFilters()},applyFilters(){this.moreParams={...this.moreParams,search:this.searchFilters.search,status:this.searchFilters.status,role_id:this.searchFilters.role_id,start:this.searchFilters.start_date,end:this.searchFilters.end_date,client_id:this.searchFilters.client_id||this.moreParams.client_id,page:1},this.offset=1,this.setUsers()},handleRowClick(t,e){console.log("Row clicked:",t,e)},handleLimitChange(t){this.limit=t,this.moreParams.limit=t,this.offset=1,this.setUsers()},async setRoles(){var e;let t=this;try{let a=await g.getRoles({limit:100});a.status===200&&(t.roles=[],(((e=a.message)==null?void 0:e.data)||a.message||[]).forEach(function(i){let o={text:i.role_name,value:i.role_id};t.roles.push(o)}))}catch(a){console.error("Error fetching roles:",a)}},getRoleName(t){let e=this.roles.find(a=>a.value===t);return e?e.text:"N/A"}}},L={class:"space-y-6"},D={class:"flex items-center justify-between"},P={class:"flex items-center space-x-3"},B={class:"font-medium text-gray-900"},N={class:"text-sm"},R={class:"text-gray-900"},T={class:"text-gray-500"},A={class:"text-sm"},O={class:"text-gray-900"},z={class:"text-gray-500"},H={class:"text-gray-900"},j={class:"text-xs"},E={class:"text-green-600"},M={class:"text-red-600"},I={class:"text-gray-900"},V={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},q={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800"},G={key:2,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800"},J=["onClick"],K=["onClick"];function Q(t,e,a,n,i,o){const p=f("router-link"),y=f("SearchFilter"),w=f("DataTable");return c(),h("div",L,[s("div",D,[e[4]||(e[4]=s("div",null,[s("h1",{class:"text-2xl font-bold text-gray-900"},"System Users"),s("p",{class:"mt-1 text-sm text-gray-500"}," Manage system users and their access permissions ")],-1)),s("div",P,[s("button",{onClick:e[0]||(e[0]=(...r)=>o.setUsers&&o.setUsers(...r)),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},e[2]||(e[2]=[s("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),u(" Refresh ")])),n.authStore.isSuperUser?(c(),k(p,{key:0,to:{name:"add-user"},class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},{default:l(()=>e[3]||(e[3]=[s("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),u(" Add System User ")])),_:1,__:[3]})):x("",!0)])]),m(y,{filters:i.searchFilters,"onUpdate:filters":e[1]||(e[1]=r=>i.searchFilters=r),"search-placeholder":"Search users by name, email, or username...","show-status-filter":!0,"show-role-filter":!0,"show-date-filter":!0,"show-client-filter":n.authStore.isSuperUser,"has-advanced-filters":!0,"role-options":i.roles,onSearch:o.handleSearch,onFilter:o.handleFilter,onClear:o.handleClearFilters},null,8,["filters","show-client-filter","role-options","onSearch","onFilter","onClear"]),m(w,{data:i.data,headers:i.tableHeaders,title:"System Users",loading:i.isLoading,searchable:!0,pagination:!0,"current-page":i.offset,"total-records":i.total,"page-size":i.limit,"has-actions":!0,"empty-message":"No users found",onPageChange:o.gotToPage,onSearch:o.handleSearch,onRowClick:o.handleRowClick,onLimitChange:o.handleLimitChange},{"header-actions":l(()=>[m(p,{to:{name:"add-user"},class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},{default:l(()=>e[5]||(e[5]=[s("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),u(" Add User ")])),_:1,__:[5]})]),"cell-user_name":l(({item:r})=>[s("div",B,d(o.explodeByUnderscore(r.user_name)),1)]),"cell-client_info":l(({item:r})=>[s("div",N,[s("div",R,d(r.client_name),1),s("div",T,d(r.account_number),1)])]),"cell-contact_info":l(({item:r})=>[s("div",A,[s("div",O,d(r.email_address),1),s("div",z,"+"+d(r.msisdn),1)])]),"cell-role_id":l(({item:r})=>[s("span",H,d(o.getRoleName(r.role_id)),1)]),"cell-login_attempts":l(({item:r})=>[s("div",j,[s("div",E,"Successful: "+d(r.cumulative_success_login),1),s("div",M,"Failed: "+d(r.cumlative_failed_attempts),1)])]),"cell-last_logged_on":l(({item:r})=>[s("span",I,d(o.formatLastLoggedOn(r.last_logged_on)),1)]),"cell-status":l(({item:r})=>[parseInt(r.status)===1?(c(),h("span",V," Active ")):parseInt(r.status)===3?(c(),h("span",q," Deactivated ")):(c(),h("span",G," Inactive "))]),actions:l(({item:r,closeDropdown:_})=>[parseInt(r.status)===1?(c(),h("button",{key:0,onClick:v=>{o.editRow(r),_()},class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"},[e[6]||(e[6]=s("svg",{class:"inline w-4 h-4 mr-2 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)),u(" Edit User "+d(o.explodeByUnderscore(r.user_name)),1)],8,J)):x("",!0),s("button",{onClick:v=>{o.resend(parseInt(r.user_id)),_()},class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"},[e[7]||(e[7]=s("svg",{class:"inline w-4 h-4 mr-2 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),u(" Send OTP to "+d(o.explodeByUnderscore(r.user_name)),1)],8,K)]),_:1},8,["data","headers","loading","current-page","total-records","page-size","onPageChange","onSearch","onRowClick","onLimitChange"])])}const te=b(U,[["render",Q]]);export{te as default};
