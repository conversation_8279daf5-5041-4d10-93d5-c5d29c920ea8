import{d as X,r as v,x as y,w as H,p as ee,A as te,c as n,b as t,f as g,t as d,h as O,C as se,F as h,m as f,I as x,v as oe,i as C,z as b,g as U,o as r}from"./index-DOaBqVmr.js";const ne={class:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"},re={class:"px-6 py-4 border-b border-gray-200 bg-gray-50"},ae={class:"flex items-center justify-between"},le={class:"flex items-center space-x-4"},ie={class:"text-lg font-medium text-gray-900"},de={class:"relative"},ce=["value"],ue={class:"flex items-center space-x-4"},ge={key:0,class:"relative"},pe={key:0,class:"flex items-center justify-center py-12"},he={key:1,class:"hidden md:block overflow-x-auto"},fe={class:"inline-block min-w-full align-middle"},be={class:"overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"},me={class:"min-w-full divide-y divide-gray-300"},ve={class:"bg-gray-50"},ye=["onClick"],xe={class:"flex items-center space-x-1"},we={key:0,class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ke={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 15l7-7 7 7"},_e={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},Ce={key:0,class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"},Me={class:"bg-white divide-y divide-gray-200"},ze=["onClick"],Pe={key:0},$e={key:0,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"},je={key:1,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"},De={key:1,class:"text-gray-900"},Se={key:2,class:"text-gray-900 font-medium"},Be={key:3,class:"text-gray-900"},Ne={key:0,class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Ve={class:"relative inline-block text-left"},Ae=["onClick"],Re={class:"py-1"},Le={key:2,class:"md:hidden divide-y divide-gray-200"},Te=["onClick"],Ee={class:"space-y-3"},He={class:"text-sm font-medium text-gray-500 w-1/3"},Oe={class:"text-sm text-gray-900 w-2/3 text-right"},Ue={key:0},Fe={key:0,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"},Ie={key:1,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"},Ke={key:1,class:"text-gray-900"},Ye={key:2,class:"text-gray-900 font-medium"},Qe={key:3,class:"text-gray-900"},Ze={key:0,class:"pt-3 border-t border-gray-200"},qe={class:"relative inline-block text-left"},Ge=["onClick"],Je={class:"py-1"},We={key:3,class:"text-center py-12"},Xe={class:"mt-1 text-sm text-gray-500"},et={key:4,class:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6"},tt={class:"flex items-center justify-between"},st={class:"flex-1 flex justify-between sm:hidden"},ot=["disabled"],nt=["disabled"],rt={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},at={class:"text-sm text-gray-700"},lt={class:"font-medium"},it={class:"font-medium"},dt={class:"font-medium"},ct={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination"},ut=["disabled"],gt=["onClick"],pt=["disabled"],ft=X({__name:"DataTable",props:{data:{default:()=>[]},headers:{default:()=>({})},title:{default:"Data Table"},loading:{type:Boolean,default:!1},searchable:{type:Boolean,default:!0},pagination:{type:Boolean,default:!0},currentPage:{default:1},totalRecords:{default:0},pageSize:{default:10},rowKey:{default:"id"},rowClass:{default:""},emptyMessage:{default:"No records found"},hasActions:{type:Boolean,default:!1}},emits:["page-change","search","sort","row-click","limit-change"],setup(F,{emit:I}){const i=F,p=I,w=v(""),M=v(""),m=v("asc"),c=v({}),k=v(i.pageSize),K=[10,25,50,100],z=y(()=>{if(Object.keys(i.headers).length>0)return i.headers;if(i.data.length>0){const e=i.data[0],o={};return Object.keys(e).forEach(s=>{o[s]=s.replace(/([a-z])([A-Z])/g,"$1 $2").replace(/_/g," ").replace(/\s+/g," ").replace(/\b\w/g,a=>a.toUpperCase()).trim()}),o}return{}}),_=y(()=>Math.ceil(i.totalRecords/i.pageSize)),Y=y(()=>(i.currentPage-1)*i.pageSize+1),Q=y(()=>Math.min(i.currentPage*i.pageSize,i.totalRecords)),Z=y(()=>{const e=[];let s=Math.max(1,i.currentPage-Math.floor(2.5)),a=Math.min(_.value,s+5-1);a-s+1<5&&(s=Math.max(1,a-5+1));for(let u=s;u<=a;u++)e.push(u);return e}),P=()=>{p("search",w.value)},q=e=>{M.value===e?m.value=m.value==="asc"?"desc":"asc":(M.value=e,m.value="asc"),p("sort",e,m.value)},$=(e,o)=>{p("row-click",e,o)},j=(e,o)=>e[i.rowKey]||o,G=(e,o)=>{const s="text-gray-900";return typeof o=="boolean"?s:typeof o=="number"?`${s} font-medium`:s},D=e=>e instanceof Date||typeof e=="string"&&!isNaN(Date.parse(e)),S=e=>typeof e=="number"&&!isNaN(e),B=e=>new Date(e).toLocaleDateString(),N=e=>e.toLocaleString(),J=e=>{p("page-change",e)},V=()=>{i.currentPage>1&&p("page-change",i.currentPage-1)},A=()=>{i.currentPage<_.value&&p("page-change",i.currentPage+1)},R=e=>{Object.keys(c.value).forEach(o=>{o!==e.toString()&&(c.value[o]=!1)}),c.value[e]=!c.value[e]},L=e=>{c.value[e]=!1},T=()=>{Object.keys(c.value).forEach(e=>{c.value[e]=!1})},W=()=>{p("limit-change",k.value)};let E;return H(w,()=>{clearTimeout(E),E=setTimeout(()=>{P()},300)}),H(()=>i.pageSize,e=>{k.value=e}),ee(()=>{document.addEventListener("click",T)}),te(()=>{document.removeEventListener("click",T)}),(e,o)=>(r(),n("div",ne,[t("div",re,[t("div",ae,[t("div",le,[t("h3",ie,d(e.title),1),t("div",de,[O(t("select",{"onUpdate:modelValue":o[0]||(o[0]=s=>k.value=s),onChange:W,class:"block w-20 pl-3 pr-8 py-2 text-sm border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"},[(r(),n(h,null,f(K,s=>t("option",{key:s,value:s},d(s),9,ce)),64))],544),[[se,k.value]])])]),t("div",ue,[e.searchable?(r(),n("div",ge,[o[4]||(o[4]=t("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[t("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1)),O(t("input",{"onUpdate:modelValue":o[1]||(o[1]=s=>w.value=s),type:"text",placeholder:"Search...",class:"block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm",onInput:P},null,544),[[oe,w.value]])])):g("",!0),x(e.$slots,"header-actions")])])]),e.loading?(r(),n("div",pe,o[5]||(o[5]=[t("div",{class:"flex items-center space-x-2 text-gray-500"},[t("svg",{class:"animate-spin h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})]),t("span",null,"Loading...")],-1)]))):e.data.length>0?(r(),n("div",he,[t("div",fe,[t("div",be,[t("table",me,[t("thead",ve,[t("tr",null,[(r(!0),n(h,null,f(z.value,(s,a)=>(r(),n("th",{key:a,class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors duration-200",onClick:u=>q(a)},[t("div",xe,[t("span",null,d(s),1),M.value===a?(r(),n("svg",we,[m.value==="asc"?(r(),n("path",ke)):(r(),n("path",_e))])):g("",!0)])],8,ye))),128)),e.hasActions?(r(),n("th",Ce," Actions ")):g("",!0)])]),t("tbody",Me,[(r(!0),n(h,null,f(e.data,(s,a)=>(r(),n("tr",{key:j(s,a),class:b(["hover:bg-gray-50 transition-colors duration-200 cursor-pointer",e.rowClass]),onClick:u=>$(s,a)},[(r(!0),n(h,null,f(z.value,(u,l)=>(r(),n("td",{key:l,class:b(["px-6 py-4 whitespace-nowrap text-sm",G(l,s[l])])},[x(e.$slots,`cell-${l}`,{item:s,value:s[l],index:a},()=>[typeof s[l]=="boolean"?(r(),n("span",Pe,[s[l]?(r(),n("span",$e," Yes ")):(r(),n("span",je," No "))])):D(s[l])?(r(),n("span",De,d(B(s[l])),1)):S(s[l])?(r(),n("span",Se,d(N(s[l])),1)):(r(),n("span",Be,d(s[l]||"-"),1))])],2))),128)),e.hasActions?(r(),n("td",Ne,[t("div",Ve,[t("button",{onClick:u=>R(a),class:b(["inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200",{"bg-gray-200":c.value[a]}])},o[6]||(o[6]=[t("svg",{class:"w-4 h-4 text-gray-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"})],-1)]),10,Ae),c.value[a]?(r(),n("div",{key:0,class:"absolute right-0 z-10 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none",onClick:o[2]||(o[2]=U(()=>{},["stop"]))},[t("div",Re,[x(e.$slots,"actions",{item:s,index:a,closeDropdown:()=>L(a)},()=>[o[7]||(o[7]=t("button",{class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"}," View ",-1))])])])):g("",!0)])])):g("",!0)],10,ze))),128))])])])])])):e.data.length>0?(r(),n("div",Le,[(r(!0),n(h,null,f(e.data,(s,a)=>(r(),n("div",{key:j(s,a),class:b(["p-4 hover:bg-gray-50 transition-colors duration-200 cursor-pointer",e.rowClass]),onClick:u=>$(s,a)},[t("div",Ee,[(r(!0),n(h,null,f(z.value,(u,l)=>(r(),n("div",{key:l,class:"flex justify-between items-start"},[t("dt",He,d(u),1),t("dd",Oe,[x(e.$slots,`cell-${l}`,{item:s,value:s[l],index:a},()=>[typeof s[l]=="boolean"?(r(),n("span",Ue,[s[l]?(r(),n("span",Fe," Yes ")):(r(),n("span",Ie," No "))])):D(s[l])?(r(),n("span",Ke,d(B(s[l])),1)):S(s[l])?(r(),n("span",Ye,d(N(s[l])),1)):(r(),n("span",Qe,d(s[l]||"-"),1))])])]))),128)),e.hasActions?(r(),n("div",Ze,[t("div",qe,[t("button",{onClick:u=>R(`mobile-${a}`),class:b(["inline-flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200",{"bg-gray-200":c.value[`mobile-${a}`]}])},o[8]||(o[8]=[t("svg",{class:"w-4 h-4 text-gray-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"})],-1)]),10,Ge),c.value[`mobile-${a}`]?(r(),n("div",{key:0,class:"absolute left-0 z-10 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none",onClick:o[3]||(o[3]=U(()=>{},["stop"]))},[t("div",Je,[x(e.$slots,"actions",{item:s,index:a,closeDropdown:()=>L(`mobile-${a}`)},()=>[o[9]||(o[9]=t("button",{class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"}," View ",-1))])])])):g("",!0)])])):g("",!0)])],10,Te))),128))])):(r(),n("div",We,[o[10]||(o[10]=t("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)),o[11]||(o[11]=t("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No data found",-1)),t("p",Xe,d(e.emptyMessage),1)])),e.pagination&&e.totalRecords>0?(r(),n("div",et,[t("div",tt,[t("div",st,[t("button",{onClick:V,disabled:e.currentPage===1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"}," Previous ",8,ot),t("button",{onClick:A,disabled:e.currentPage===_.value,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"}," Next ",8,nt)]),t("div",rt,[t("div",null,[t("p",at,[o[12]||(o[12]=C(" Showing ")),t("span",lt,d(Y.value),1),o[13]||(o[13]=C(" to ")),t("span",it,d(Q.value),1),o[14]||(o[14]=C(" of ")),t("span",dt,d(e.totalRecords),1),o[15]||(o[15]=C(" results "))])]),t("div",null,[t("nav",ct,[t("button",{onClick:V,disabled:e.currentPage===1,class:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"},o[16]||(o[16]=[t("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)]),8,ut),(r(!0),n(h,null,f(Z.value,s=>(r(),n("button",{key:s,onClick:a=>J(s),class:b(["relative inline-flex items-center px-4 py-2 border text-sm font-medium transition-colors duration-200",s===e.currentPage?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"])},d(s),11,gt))),128)),t("button",{onClick:A,disabled:e.currentPage===_.value,class:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"},o[17]||(o[17]=[t("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]),8,pt)])])])])])):g("",!0)]))}});export{ft as _};
