import{d as j,r as p,x as D,p as F,c as i,b as e,j as I,i as L,y as k,h as g,v as C,C as q,F as x,m as v,t as d,g as O,q as z,n as P,f as G,D as H,o as a}from"./index-DOaBqVmr.js";import{s as y}from"./systemApi-CvVfCghC.js";import{R as J}from"./permissions-vZl7iLZK.js";import{r as K}from"./ArrowLeftIcon-DKfvydGS.js";const Q={class:"space-y-6"},W={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},X={class:"flex items-center justify-between"},Y={key:0,class:"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center"},Z={key:1,class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},ee={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},se={class:"mt-4"},te={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},oe=["onClick"],re={class:"font-medium text-gray-900"},le={class:"text-sm text-gray-500 mt-1"},ne={class:"mt-2"},ie={class:"text-xs text-blue-600"},ae={class:"bg-gray-50 rounded-lg p-4"},de={class:"flex items-center justify-between mb-4"},ue={class:"text-sm font-medium text-gray-700"},ce={class:"max-h-80 overflow-y-auto space-y-4"},me={class:"px-3 py-2 bg-white border-b border-gray-200 rounded-t-lg"},pe={class:"flex items-center"},be=["id","checked","onChange"],ge=["for"],fe={class:"ml-auto text-xs text-gray-500"},xe={class:"px-3 py-2 space-y-2"},ve=["id","value"],ye=["for"],he={key:0,class:"text-xs text-gray-400 ml-2"},_e={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200"},we=["disabled"],Se=j({__name:"EditRole",setup(ke){const c=P(),R=z(),b=p(!1),h=p(!0),f=p([]),u=p(null),r=p({role_id:0,role_name:"",description:"",permissions:[],status:1}),E=Object.values(J).map(t=>({id:t.id,name:t.name,description:t.description,permissions:t.permissions})),A=D(()=>{const t={};return f.value.forEach(s=>{const o=s.module||"general";t[o]||(t[o]=[]),t[o].push(s)}),t}),S=async()=>{var s,o;const t=R.params.id;if(!t){c.push({name:"system-roles"});return}try{const l=await y.getRoles({limit:1e3});if(l.status===200){const n=((s=l.message)==null?void 0:s.data)||[];u.value=n.find(m=>m.role_id===parseInt(t))||null,u.value?r.value={role_id:u.value.role_id,role_name:u.value.role_name,description:u.value.description||"",permissions:((o=u.value.permissions)==null?void 0:o.map(m=>m.id))||[],status:u.value.status}:c.push({name:"system-roles"})}}catch(l){console.error("Error fetching role:",l),c.push({name:"system-roles"})}finally{h.value=!1}},M=async()=>{var t;try{const s=await y.getPermissions({limit:1e3});s.status===200&&(f.value=((t=s.message)==null?void 0:t.data)||[])}catch(s){console.error("Error fetching permissions:",s)}},U=t=>{confirm(`Apply template "${t.name}"? This will replace current permissions.`)&&(r.value.permissions=[...t.permissions])},_=t=>t.every(s=>r.value.permissions.includes(s.id)),V=t=>{_(t)?t.forEach(o=>{const l=r.value.permissions.indexOf(o.id);l>-1&&r.value.permissions.splice(l,1)}):t.forEach(o=>{r.value.permissions.includes(o.id)||r.value.permissions.push(o.id)})},$=()=>{r.value.permissions=f.value.map(t=>t.id)},B=()=>{r.value.permissions=[]},N=async()=>{b.value=!0;try{const t=await y.updateRole({role_id:r.value.role_id,role_name:r.value.role_name,description:r.value.description,permissions:r.value.permissions,status:r.value.status});t.status===200?c.push({name:"system-roles"}):console.error("Failed to update role:",t.message)}catch(t){console.error("Error updating role:",t)}finally{b.value=!1}},w=()=>{c.push({name:"system-roles"})},T=t=>t.charAt(0).toUpperCase()+t.slice(1).replace(/[_-]/g," ");return F(async()=>{await M(),await S()}),(t,s)=>(a(),i("div",Q,[e("div",W,[e("div",X,[s[5]||(s[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Edit Role"),e("p",{class:"text-gray-600 mt-1"},"Update role information and permissions")],-1)),e("button",{onClick:w,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[I(k(K),{class:"w-4 h-4 mr-2"}),s[4]||(s[4]=L(" Back to Roles "))])])]),h.value?(a(),i("div",Y,s[6]||(s[6]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"},null,-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Loading role data...",-1)]))):(a(),i("div",Z,[e("form",{onSubmit:O(N,["prevent"]),class:"space-y-6"},[e("div",null,[s[11]||(s[11]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Basic Information",-1)),e("div",ee,[e("div",null,[s[7]||(s[7]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Role Name *",-1)),g(e("input",{"onUpdate:modelValue":s[0]||(s[0]=o=>r.value.role_name=o),type:"text",required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter role name"},null,512),[[C,r.value.role_name]])]),e("div",null,[s[9]||(s[9]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Status",-1)),g(e("select",{"onUpdate:modelValue":s[1]||(s[1]=o=>r.value.status=o),class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},s[8]||(s[8]=[e("option",{value:1},"Active",-1),e("option",{value:0},"Inactive",-1)]),512),[[q,r.value.status]])])]),e("div",se,[s[10]||(s[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Description",-1)),g(e("textarea",{"onUpdate:modelValue":s[2]||(s[2]=o=>r.value.description=o),rows:"3",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Describe the role and its purpose"},null,512),[[C,r.value.description]])])]),e("div",null,[s[12]||(s[12]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Role Templates",-1)),s[13]||(s[13]=e("p",{class:"text-sm text-gray-600 mb-4"},"Apply a template to quickly set permissions",-1)),e("div",te,[(a(!0),i(x,null,v(k(E),o=>(a(),i("div",{key:o.id,onClick:l=>U(o),class:"border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200"},[e("h4",re,d(o.name),1),e("p",le,d(o.description),1),e("div",ne,[e("span",ie,d(o.permissions.length)+" permissions",1)])],8,oe))),128))])]),e("div",null,[s[14]||(s[14]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Permissions",-1)),e("div",ae,[e("div",de,[e("span",ue,d(r.value.permissions.length)+" permissions selected ",1),e("div",{class:"flex space-x-2"},[e("button",{type:"button",onClick:$,class:"text-sm text-blue-600 hover:text-blue-800"}," Select All "),e("button",{type:"button",onClick:B,class:"text-sm text-gray-600 hover:text-gray-800"}," Clear All ")])]),e("div",ce,[(a(!0),i(x,null,v(A.value,(o,l)=>(a(),i("div",{key:l,class:"border border-gray-200 rounded-lg"},[e("div",me,[e("div",pe,[e("input",{id:`module-${l}`,type:"checkbox",checked:_(o),onChange:n=>V(o),class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,40,be),e("label",{for:`module-${l}`,class:"ml-2 text-sm font-medium text-gray-900 capitalize"},d(T(l)),9,ge),e("span",fe,d(o.length)+" permissions ",1)])]),e("div",xe,[(a(!0),i(x,null,v(o,n=>(a(),i("div",{key:n.id,class:"flex items-center"},[g(e("input",{id:`permission-${n.id}`,"onUpdate:modelValue":s[3]||(s[3]=m=>r.value.permissions=m),value:n.id,type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,8,ve),[[H,r.value.permissions]]),e("label",{for:`permission-${n.id}`,class:"ml-2 text-sm text-gray-700 flex-1"},d(n.name),9,ye),n.description?(a(),i("span",he,d(n.description),1)):G("",!0)]))),128))])]))),128))])])]),e("div",_e,[e("button",{type:"button",onClick:w,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Cancel "),e("button",{type:"submit",disabled:b.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},d(b.value?"Updating...":"Update Role"),9,we)])],32)]))]))}});export{Se as default};
