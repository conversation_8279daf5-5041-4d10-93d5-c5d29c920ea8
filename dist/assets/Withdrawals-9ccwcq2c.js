import{d as Q,u as G,r as i,a as J,p as K,c as g,b as e,j as p,i as D,y as v,h as y,v as h,C as X,k as l,f as w,t as o,z as Y,n as Z,o as x}from"./index-DOaBqVmr.js";import{_ as ee}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{b as te}from"./billPaymentsApi-BYuF0XYT.js";import{r as se}from"./ArrowPathIcon-BCh5HUKO.js";import{r as re}from"./EllipsisVerticalIcon-BCCOPEML.js";import{r as oe}from"./ArrowDownTrayIcon-BWjpub36.js";const ae={class:"space-y-6"},le={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},ne={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},ie={class:"mt-4 sm:mt-0 flex space-x-3"},ce={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},ue={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},de={class:"flex space-x-2"},me={class:"text-sm font-medium text-gray-900"},fe={class:"text-sm font-medium text-gray-900"},be={class:"text-sm"},ge={class:"font-medium text-gray-900"},pe={class:"text-gray-500"},ye={class:"text-sm text-gray-900"},xe={class:"text-sm font-medium text-gray-900"},ve={class:"text-sm font-medium text-gray-900"},he={class:"text-sm text-gray-900"},we={class:"relative"},_e=["onClick"],ke={key:0,class:"absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},Ce={class:"py-1"},Se=["onClick"],De=["onClick"],Ae=["onClick"],je=["onClick"],We=Q({__name:"Withdrawals",setup(Ve){const A=Z();G();const c=i(!1),m=i([]),u=i(1),f=i(0),_=i(10),j=i(""),V=i(""),F=i("asc"),d=J({}),a=i({reference_number:"",withdrawal_status:"",start:"",end:"",receipt_number:"",loan_number:"",client_id:"",client_phone:""}),P=[{key:"reference_number",label:"Reference No",sortable:!0},{key:"receipt_number",label:"Receipt No",sortable:!0},{key:"customer",label:"Profile Acc",sortable:!1},{key:"description",label:"Description",sortable:!1},{key:"amount",label:"Amount",sortable:!0},{key:"charges",label:"Charges",sortable:!0},{key:"created",label:"Date",sortable:!0},{key:"status",label:"Status",sortable:!0}],n=async()=>{var r,t;c.value=!0;try{const s=await te.getWithdrawals({page:u.value,limit:_.value,...a.value});s.status===200?(m.value=((r=s.message)==null?void 0:r.data)||[],f.value=((t=s.message)==null?void 0:t.total_count)||0):(m.value=[],f.value=0)}catch(s){console.error("Error fetching withdrawals:",s),m.value=[],f.value=0}finally{c.value=!1}},R=r=>{u.value=r,n()},$=r=>{j.value=r,u.value=1,n()},E=(r,t)=>{V.value=r,F.value=t,n()},N=r=>{k(r)},W=()=>{n()},z=()=>{u.value=1,n()},T=()=>{a.value={reference_number:"",withdrawal_status:"",start:"",end:"",receipt_number:"",loan_number:"",client_id:"",client_phone:""},u.value=1,n()},M=r=>{Object.keys(d).forEach(t=>{parseInt(t)!==r&&(d[parseInt(t)]=!1)}),d[r]=!d[r]},k=r=>{console.log("View details for withdrawal:",r.reference_number)},U=r=>{A.push({name:"transactions",query:{loan_number:r.loan_number}})},B=async r=>{try{if(!confirm(`Are you sure you want to approve withdrawal ${r.reference_number}?`))return;c.value=!0,console.log("Approve withdrawal:",r.reference_number),alert("Withdrawal approved successfully"),n()}catch(t){console.error("Error approving withdrawal:",t),alert("Failed to approve withdrawal. Please try again.")}finally{c.value=!1}},q=async r=>{try{if(!confirm(`Are you sure you want to reject withdrawal ${r.reference_number}?`))return;c.value=!0,console.log("Reject withdrawal:",r.reference_number),alert("Withdrawal rejected successfully"),n()}catch(t){console.error("Error rejecting withdrawal:",t),alert("Failed to reject withdrawal. Please try again.")}finally{c.value=!1}},I=()=>{console.log("Export withdrawals data")},C=r=>(typeof r=="string"?parseFloat(r):r).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),H=r=>{if(!r)return"N/A";try{return new Date(r).toLocaleString()}catch{return"N/A"}},L=r=>({1:"Successful",0:"Failed",2:"Pending"})[r]||"Unknown",O=r=>({1:"bg-green-100 text-green-800",0:"bg-red-100 text-red-800",2:"bg-yellow-100 text-yellow-800"})[r]||"bg-gray-100 text-gray-800";return K(()=>{n()}),(r,t)=>(x(),g("div",ae,[e("div",le,[e("div",ne,[t[5]||(t[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Withdrawals"),e("p",{class:"text-gray-600 mt-1"},"Process and manage withdrawal requests")],-1)),e("div",ie,[e("button",{onClick:W,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[p(v(se),{class:"w-4 h-4 mr-2"}),t[4]||(t[4]=D(" Refresh "))])])])]),e("div",ce,[e("div",ue,[e("div",null,[t[6]||(t[6]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Reference Number",-1)),y(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>a.value.reference_number=s),type:"text",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter reference number"},null,512),[[h,a.value.reference_number]])]),e("div",null,[t[8]||(t[8]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Status",-1)),y(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>a.value.withdrawal_status=s),class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},t[7]||(t[7]=[e("option",{value:""},"All Status",-1),e("option",{value:"1"},"Successful",-1),e("option",{value:"0"},"Failed",-1),e("option",{value:"2"},"Pending",-1)]),512),[[X,a.value.withdrawal_status]])]),e("div",null,[t[9]||(t[9]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Start Date",-1)),y(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>a.value.start=s),type:"date",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[h,a.value.start]])]),e("div",null,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"End Date",-1)),y(e("input",{"onUpdate:modelValue":t[3]||(t[3]=s=>a.value.end=s),type:"date",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[h,a.value.end]])])]),e("div",{class:"mt-4 flex justify-end space-x-3"},[e("button",{onClick:T,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Clear "),e("button",{onClick:z,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Apply Filters ")])]),p(ee,{data:m.value,headers:P,loading:c.value,"current-page":u.value,"total-records":f.value,"page-size":_.value,title:"Withdrawal Transactions","row-key":"reference_number","has-actions":!0,onPageChange:R,onSearch:$,onSort:E,onRowClick:N},{"header-actions":l(()=>[e("div",de,[e("button",{onClick:I,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[p(v(oe),{class:"w-4 h-4 mr-2"}),t[11]||(t[11]=D(" Export "))])])]),"cell-reference_number":l(({row:s})=>[e("div",me,o(s.reference_number),1)]),"cell-receipt_number":l(({row:s})=>[e("div",fe,o(s.receipt_number),1)]),"cell-customer":l(({row:s})=>[e("div",be,[e("div",ge,"+"+o(s.customer?s.customer.split("-")[0]:""),1),e("div",pe,o(s.customer?s.customer.split("-")[1]:""),1)])]),"cell-description":l(({row:s})=>[e("div",ye,o(s.response_desc),1)]),"cell-amount":l(({row:s})=>[e("div",xe,o(s.currency_code)+"."+o(C(s.amount)),1)]),"cell-charges":l(({row:s})=>[e("div",ve,o(s.currency_code)+"."+o(C(s.charges)),1)]),"cell-created":l(({row:s})=>[e("div",he,o(H(s.created)),1)]),"cell-status":l(({row:s})=>[e("span",{class:Y([O(s.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(L(s.status)),3)]),actions:l(({row:s,index:S})=>[e("div",we,[e("button",{onClick:b=>M(S),class:"inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[p(v(re),{class:"w-4 h-4"})],8,_e),d[S]?(x(),g("div",ke,[e("div",Ce,[e("button",{onClick:b=>k(s),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," View Details ",8,Se),e("button",{onClick:b=>U(s),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-900"}," View Transactions ",8,De),s.status===2?(x(),g("button",{key:0,onClick:b=>B(s),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"}," Approve ",8,Ae)):w("",!0),s.status===2?(x(),g("button",{key:1,onClick:b=>q(s),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"}," Reject ",8,je)):w("",!0)])])):w("",!0)])]),_:1},8,["data","loading","current-page","total-records","page-size"])]))}});export{We as default};
