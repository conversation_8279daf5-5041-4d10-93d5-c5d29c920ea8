import{d as y,r as m,a as _,p as h,q as k,c as l,b as e,e as C,g as V,f as p,t as f,h as i,v as u,i as b,j as P,k as M,l as j,s as B,n as R,o as c}from"./index-DOaBqVmr.js";const q={class:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8"},N={class:"max-w-md w-full space-y-8"},S={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},U={key:0,class:"mb-4 p-4 rounded-md bg-red-50 border border-red-200"},E={class:"flex"},A={class:"ml-3"},T={class:"text-sm text-red-800"},D={key:1,class:"mb-4 p-4 rounded-md bg-green-50 border border-green-200"},H={class:"flex"},z={class:"ml-3"},F={class:"text-sm text-green-800"},I={class:"space-y-4"},L={class:"mt-6"},G=["disabled"],J={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},K={class:"mt-4 text-center"},W=y({__name:"ResetPassword",setup(O){const v=R(),w=k(),n=m(!1),t=m(""),a=m(""),o=_({username:"",verification_code:"",new_password:"",confirm_password:"",dial_code:"254"}),x=async()=>{if(t.value="",a.value="",o.new_password!==o.confirm_password){t.value="Passwords do not match";return}if(o.new_password.length<6){t.value="Password must be at least 6 characters long";return}n.value=!0;try{const d=await B.resetPassword({username:o.username,new_password:o.new_password,verification_code:o.verification_code,dial_code:o.dial_code});d.status===200?(a.value="Password reset successfully! Redirecting to login...",setTimeout(()=>{v.push({name:"login"})},2e3)):t.value=d.message||"Failed to reset password"}catch(d){t.value=d.message||"An unexpected error occurred"}finally{n.value=!1}};return h(()=>{w.query.username&&(o.username=w.query.username)}),(d,s)=>{const g=j("router-link");return c(),l("div",q,[e("div",N,[s[12]||(s[12]=C('<div class="text-center"><div class="mx-auto h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg"><span class="text-white font-bold text-xl">S</span></div><h2 class="mt-6 text-3xl font-extrabold text-gray-900"> Reset Password </h2><p class="mt-2 text-sm text-gray-600"> Enter the verification code and your new password </p></div>',1)),e("form",{class:"mt-8 space-y-6",onSubmit:V(x,["prevent"])},[e("div",S,[t.value?(c(),l("div",U,[e("div",E,[s[4]||(s[4]=e("svg",{class:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",A,[e("p",T,f(t.value),1)])])])):p("",!0),a.value?(c(),l("div",D,[e("div",H,[s[5]||(s[5]=e("svg",{class:"h-5 w-5 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)),e("div",z,[e("p",F,f(a.value),1)])])])):p("",!0),e("div",I,[e("div",null,[s[6]||(s[6]=e("label",{for:"username",class:"block text-sm font-medium text-gray-700"}," Username ",-1)),i(e("input",{id:"username","onUpdate:modelValue":s[0]||(s[0]=r=>o.username=r),type:"text",readonly:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500"},null,512),[[u,o.username]])]),e("div",null,[s[7]||(s[7]=e("label",{for:"verification_code",class:"block text-sm font-medium text-gray-700"}," Verification Code ",-1)),i(e("input",{id:"verification_code","onUpdate:modelValue":s[1]||(s[1]=r=>o.verification_code=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter verification code"},null,512),[[u,o.verification_code]])]),e("div",null,[s[8]||(s[8]=e("label",{for:"new_password",class:"block text-sm font-medium text-gray-700"}," New Password ",-1)),i(e("input",{id:"new_password","onUpdate:modelValue":s[2]||(s[2]=r=>o.new_password=r),type:"password",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter new password"},null,512),[[u,o.new_password]])]),e("div",null,[s[9]||(s[9]=e("label",{for:"confirm_password",class:"block text-sm font-medium text-gray-700"}," Confirm Password ",-1)),i(e("input",{id:"confirm_password","onUpdate:modelValue":s[3]||(s[3]=r=>o.confirm_password=r),type:"password",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Confirm new password"},null,512),[[u,o.confirm_password]])])]),e("div",L,[e("button",{type:"submit",disabled:n.value,class:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"},[n.value?(c(),l("svg",J,s[10]||(s[10]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):p("",!0),b(" "+f(n.value?"Resetting...":"Reset Password"),1)],8,G)]),e("div",K,[P(g,{to:{name:"login"},class:"text-sm text-blue-600 hover:text-blue-500 transition-colors duration-200"},{default:M(()=>s[11]||(s[11]=[b(" Back to Sign In ")])),_:1,__:[11]})])])],32)])])}}});export{W as default};
