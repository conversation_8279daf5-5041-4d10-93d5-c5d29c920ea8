import{d as we,u as ke,r as a,a as Ce,x as _,p as Ae,c as r,b as e,j as w,f as S,t as l,i as P,y as D,h as k,C as M,F as R,m as K,v as H,H as Se,e as ze,k as c,z as De,o as i}from"./index-DOaBqVmr.js";import{_ as Me}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{_ as Fe}from"./FilterCards.vue_vue_type_script_setup_true_lang-Bk3N6i80.js";import{l as Le}from"./loanApi-CajqAy4f.js";import{r as Ue}from"./PlusIcon-BKTWa0k6.js";import{r as Ve}from"./UserGroupIcon-CvI7Mo90.js";import{r as $e}from"./CurrencyDollarIcon-C8tInHXu.js";const Ie={class:"space-y-6"},Ne={class:"flex items-center justify-between"},Oe={class:"mt-1 text-sm text-gray-500"},je={class:"grid grid-cols-1 md:grid-cols-5 gap-6"},Ee={class:"bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200"},Be={class:"p-5"},Te={class:"flex items-center"},Pe={class:"flex-shrink-0"},Re={class:"ml-5 w-0 flex-1"},Ke={class:"text-lg font-medium text-gray-900"},He={class:"bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200"},Ye={class:"p-5"},Qe={class:"flex items-center"},We={class:"ml-5 w-0 flex-1"},qe={class:"text-lg font-medium text-gray-900"},Ge={class:"bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200"},Je={class:"p-5"},Xe={class:"flex items-center"},Ze={class:"ml-5 w-0 flex-1"},et={class:"text-lg font-medium text-gray-900"},tt={class:"bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200"},st={class:"p-5"},ot={class:"flex items-center"},lt={class:"ml-5 w-0 flex-1"},nt={class:"text-lg font-medium text-gray-900"},at={class:"bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200"},rt={class:"p-5"},it={class:"flex items-center"},dt={class:"flex-shrink-0"},ut={class:"ml-5 w-0 flex-1"},ct={class:"text-lg font-medium text-gray-900"},vt={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},mt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},gt=["value"],ft={class:"block text-sm font-medium text-gray-700 mb-2"},xt=["placeholder"],bt=["value"],_t={class:"text-sm"},pt={class:"font-medium text-gray-900"},ht={class:"text-gray-500"},yt={class:"font-medium text-blue-600"},wt={class:"text-sm"},kt={class:"font-medium text-gray-900"},Ct={class:"text-gray-500"},At={class:"text-sm"},St={class:"font-medium text-gray-900"},zt={class:"text-gray-500"},Dt={class:"text-sm font-medium text-green-600"},Mt={class:"text-sm"},Ft={class:"text-gray-900"},Lt={class:"text-gray-500"},Ut={class:"text-sm text-gray-900"},Vt={key:0,class:"relative"},$t=["onClick"],It={key:0,class:"absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-md shadow-lg z-10"},Nt={class:"py-1"},Ot=["onClick"],jt=["onClick"],Et=["onClick"],Bt=["onClick"],Tt=["onClick"],Pt=["onClick"],Rt=["onClick"],Kt={key:1,class:"text-gray-400"},Zt=we({__name:"LoanAccounts",setup(Ht){const p=ke(),f=a(!1),n=a([]),Y=a([]),x=a(1),h=a(0),F=a(10),L=a(""),Q=a(""),W=a("asc"),q=a(!1),d=Ce({}),m=a(null),C=a(""),z=a(""),U=a({start:"",end:""}),u=a({client_id:p.selectedClientId||"",status:"",loan_number:"",msisdn:"",national_id:"",start_date:"",end_date:""}),G=[{value:"1000",label:"Active"},{value:"1001",label:"Inactive"},{value:"1002",label:"Suspended"},{value:"1003",label:"Blacklisted"}],J=[{text:"Organization Name",value:"organization"},{text:"Loan Number",value:"loan_number"},{text:"Email",value:"email"},{text:"Phone Number",value:"phone"}],X={first_name:"Customer Name",loan_number:"Loan Account No",msisdn:"Phone",nationality:"Document Type",max_approved_loan_amount:"Max Limit",actual_balance:"Wallet",status:"Status",created:"Date"},Z=_(()=>p.selectedClient),ee=_(()=>p.hasPermission("loan_accounts_create")),te=_(()=>p.hasPermission("loan_accounts_edit")),se=_(()=>n.value.filter(s=>s.status===1e3).length),oe=_(()=>n.value.filter(s=>s.status===1003).length),le=_(()=>n.value.filter(s=>s.status===1005).length),ne=_(()=>n.value.reduce((s,t)=>s+t.max_approved_loan_amount,0)),v=async()=>{var s,t;f.value=!0;try{const g=await Le.getLoanAccounts({page:x.value,limit:F.value,search:L.value,status:z.value,client_id:u.value.client_id,loan_number:u.value.loan_number,msisdn:u.value.msisdn,national_id:u.value.national_id,start_date:u.value.start_date,end_date:u.value.end_date});g.status===200?(n.value=((s=g.message)==null?void 0:s.data)||[],h.value=((t=g.message)==null?void 0:t.total_count)||0):(n.value=[],h.value=0)}catch(g){console.error("Error fetching loan accounts:",g),n.value=[],h.value=0}finally{f.value=!1}},ae=s=>{x.value=s,v()},re=s=>{L.value=s,x.value=1,v()},ie=(s,t)=>{Q.value=s,W.value=t,x.value=1,v()},de=s=>{console.log("Row clicked:",s)},ue=()=>{v()},ce=s=>{Object.assign(u.value,s)},y=s=>{s&&(u.value={...s}),x.value=1,v()},V=()=>{u.value={client_id:p.selectedClientId||"",status:"",loan_number:"",msisdn:"",national_id:"",start_date:"",end_date:""},x.value=1,v()},ve=s=>{Object.keys(d).forEach(t=>{parseInt(t)!==s&&(d[parseInt(t)]=!1)}),d[s]=!d[s]},me=async s=>{try{f.value=!0,console.log("Suspending account:",s.loan_number),await v()}catch(t){console.error("Error suspending account:",t)}finally{f.value=!1,d[n.value.indexOf(s)]=!1}},ge=async s=>{try{f.value=!0,console.log("Activating account:",s.loan_number),await v()}catch(t){console.error("Error activating account:",t)}finally{f.value=!1,d[n.value.indexOf(s)]=!1}},fe=s=>{console.log("Edit account:",s.loan_number),d[n.value.indexOf(s)]=!1},xe=s=>{console.log("View transactions for:",s.loan_number),d[n.value.indexOf(s)]=!1},be=s=>{console.log("Check KYC for:",s.loan_number),d[n.value.indexOf(s)]=!1},_e=s=>{console.log("Blacklist account:",s.loan_number),d[n.value.indexOf(s)]=!1},pe=s=>{console.log("Unblock account:",s.loan_number),d[n.value.indexOf(s)]=!1},A=s=>new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(s),$=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),he=s=>{switch(s){case 1e3:return"bg-green-100 text-green-800";case 1002:return"bg-blue-100 text-blue-800";case 1003:return"bg-yellow-100 text-yellow-800";case 1004:return"bg-gray-100 text-gray-800";case 1005:return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},ye=s=>{switch(s){case 1e3:return"Active";case 1002:return"New";case 1003:return"Unverified";case 1004:return"Dormant";case 1005:return"Suspended";default:return"Unknown"}};return Ae(()=>{v()}),(s,t)=>{var g,I,N,O,j,E,B;return i(),r("div",Ie,[e("div",Ne,[e("div",null,[t[6]||(t[6]=e("h1",{class:"text-2xl font-bold text-gray-900"},"Loan Accounts",-1)),e("p",Oe," Manage customer loan accounts for "+l(((g=Z.value)==null?void 0:g.client_name)||"All Organizations"),1)]),ee.value?(i(),r("button",{key:0,onClick:t[0]||(t[0]=o=>q.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[w(D(Ue),{class:"h-4 w-4 mr-2"}),t[7]||(t[7]=P(" Add Loan Account "))])):S("",!0)]),e("div",je,[e("div",Ee,[e("div",Be,[e("div",Te,[e("div",Pe,[w(D(Ve),{class:"h-6 w-6 text-gray-400"})]),e("div",Re,[e("dl",null,[t[8]||(t[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Accounts",-1)),e("dd",Ke,l(h.value),1)])])])])]),e("div",He,[e("div",Ye,[e("div",Qe,[t[10]||(t[10]=e("div",{class:"flex-shrink-0"},[e("div",{class:"h-6 w-6 bg-green-100 rounded-full flex items-center justify-center"},[e("div",{class:"h-3 w-3 bg-green-500 rounded-full"})])],-1)),e("div",We,[e("dl",null,[t[9]||(t[9]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Active",-1)),e("dd",qe,l(se.value),1)])])])])]),e("div",Ge,[e("div",Je,[e("div",Xe,[t[12]||(t[12]=e("div",{class:"flex-shrink-0"},[e("div",{class:"h-6 w-6 bg-yellow-100 rounded-full flex items-center justify-center"},[e("div",{class:"h-3 w-3 bg-yellow-500 rounded-full"})])],-1)),e("div",Ze,[e("dl",null,[t[11]||(t[11]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Unverified",-1)),e("dd",et,l(oe.value),1)])])])])]),e("div",tt,[e("div",st,[e("div",ot,[t[14]||(t[14]=e("div",{class:"flex-shrink-0"},[e("div",{class:"h-6 w-6 bg-red-100 rounded-full flex items-center justify-center"},[e("div",{class:"h-3 w-3 bg-red-500 rounded-full"})])],-1)),e("div",lt,[e("dl",null,[t[13]||(t[13]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Suspended",-1)),e("dd",nt,l(le.value),1)])])])])]),e("div",at,[e("div",rt,[e("div",it,[e("div",dt,[w(D($e),{class:"h-6 w-6 text-gray-400"})]),e("div",ut,[e("dl",null,[t[15]||(t[15]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Limits",-1)),e("dd",ct,l(A(ne.value)),1)])])])])])]),e("div",vt,[e("div",mt,[e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Filter Type",-1)),k(e("select",{"onUpdate:modelValue":t[1]||(t[1]=o=>m.value=o),class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},[t[16]||(t[16]=e("option",{value:""},"Select filter type",-1)),(i(),r(R,null,K(J,o=>e("option",{key:o.value,value:o},l(o.text),9,gt)),64))],512),[[M,m.value]])]),e("div",null,[e("label",ft,l(((I=m.value)==null?void 0:I.text)||"Filter Value"),1),((N=m.value)==null?void 0:N.value)==="phone"||((O=m.value)==null?void 0:O.value)==="email"||((j=m.value)==null?void 0:j.value)==="loan_number"?k((i(),r("input",{key:0,"onUpdate:modelValue":t[2]||(t[2]=o=>C.value=o),type:"text",placeholder:`Enter ${((E=m.value)==null?void 0:E.text)||"value"}`,class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",onKeyup:Se(y,["enter"])},null,40,xt)),[[H,C.value]]):((B=m.value)==null?void 0:B.value)==="organization"?k((i(),r("select",{key:1,"onUpdate:modelValue":t[3]||(t[3]=o=>C.value=o),class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},[t[18]||(t[18]=e("option",{value:""},"Select organization",-1)),(i(!0),r(R,null,K(Y.value,o=>(i(),r("option",{key:o.client_id,value:o.client_id},l(o.client_name),9,bt))),128))],512)),[[M,C.value]]):S("",!0)]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Status",-1)),k(e("select",{"onUpdate:modelValue":t[4]||(t[4]=o=>z.value=o),class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",onChange:y},t[19]||(t[19]=[ze('<option value="">All Statuses</option><option value="1000">Active</option><option value="1002">New</option><option value="1003">Unverified</option><option value="1004">Dormant</option><option value="1005">Suspended</option>',6)]),544),[[M,z.value]])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Date Range",-1)),k(e("input",{type:"date","onUpdate:modelValue":t[5]||(t[5]=o=>U.value.start=o),class:"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",onChange:y},null,544),[[H,U.value.start]])])]),e("div",{class:"mt-4 flex justify-end space-x-3"},[e("button",{onClick:V,class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"}," Clear Filters "),e("button",{onClick:y,class:"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"}," Apply Filters ")])]),w(Fe,{filters:u.value,"filter-type":"accounts","status-options":G,"onUpdate:filters":ce,onApply:y,onClear:V},null,8,["filters"]),w(Me,{data:n.value,headers:X,loading:f.value,"current-page":x.value,"total-records":h.value,"page-size":F.value,title:"Loan Accounts","row-key":"user_id","has-actions":!0,onPageChange:ae,onSearch:re,onSort:ie,onRowClick:de},{"header-actions":c(()=>[e("button",{onClick:ue,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},t[22]||(t[22]=[e("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),P(" Refresh ")]))]),"cell-first_name":c(({item:o})=>[e("div",_t,[e("div",pt,l(o.first_name)+" "+l(o.last_name),1),e("div",ht,l(o.email_address),1)])]),"cell-loan_number":c(({value:o})=>[e("div",yt,l(o),1)]),"cell-msisdn":c(({item:o})=>[e("div",wt,[e("div",kt,"+"+l(o.msisdn),1),e("div",Ct,l(o.network),1)])]),"cell-nationality":c(({item:o})=>[e("div",At,[e("div",St,l(o.nationality)+" | "+l(o.national_id),1),e("div",zt,l(o.gender)+" | "+l($(o.dob)),1)])]),"cell-max_approved_loan_amount":c(({value:o})=>[e("div",Dt," KES "+l(A(o)),1)]),"cell-actual_balance":c(({item:o})=>[e("div",Mt,[e("div",Ft,"Actual: "+l(A(o.actual_balance)),1),e("div",Lt,"Loan: "+l(A(o.loan_balance)),1)])]),"cell-status":c(({value:o})=>[e("span",{class:De(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",he(parseInt(o))])},l(ye(parseInt(o))),3)]),"cell-created":c(({value:o})=>[e("div",Ut,l($(o)),1)]),actions:c(({item:o,index:T})=>[te.value&&(parseInt(o.status)===1e3||parseInt(o.status)===1005)?(i(),r("div",Vt,[e("button",{onClick:b=>ve(T),class:"inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[23]||(t[23]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})],-1)]),8,$t),d[T]?(i(),r("div",It,[e("div",Nt,[parseInt(o.status)===1e3?(i(),r("button",{key:0,onClick:b=>me(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"}," Suspend "+l(o.first_name),9,Ot)):parseInt(o.status)===1005?(i(),r("button",{key:1,onClick:b=>ge(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"}," Activate "+l(o.first_name),9,jt)):S("",!0),e("button",{onClick:b=>fe(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"}," Edit Account ",8,Et),e("button",{onClick:b=>xe(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"}," View Transactions ",8,Bt),e("button",{onClick:b=>be(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"}," Check KYC ",8,Tt),parseInt(o.black_list_state)===0?(i(),r("button",{key:2,onClick:b=>_e(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-yellow-50 hover:text-yellow-900"}," Blacklist ",8,Pt)):(i(),r("button",{key:3,onClick:b=>pe(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-900"}," Unblock ",8,Rt))])])):S("",!0)])):(i(),r("div",Kt,t[24]||(t[24]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})],-1)])))]),_:1},8,["data","loading","current-page","total-records","page-size"])])}}});export{Zt as default};
