import{_ as J}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{l as K}from"./loanApi-CajqAy4f.js";import{c as m,b as e,o as v,d as W,u as X,r as a,a as Y,x as g,p as Z,j as x,f as w,t as r,i as A,y,k as d,z as ee}from"./index-DOaBqVmr.js";import{r as te}from"./PlusIcon-BKTWa0k6.js";import{r as oe}from"./CreditCardIcon-Bp2xwmwY.js";import{r as se}from"./CurrencyDollarIcon-C8tInHXu.js";function ne(M,f){return v(),m("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"})])}const re={class:"space-y-6"},ae={class:"flex items-center justify-between"},le={class:"mt-1 text-sm text-gray-500"},ie={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},de={class:"bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200"},ce={class:"p-5"},ue={class:"flex items-center"},me={class:"flex-shrink-0"},ve={class:"ml-5 w-0 flex-1"},ge={class:"text-lg font-medium text-gray-900"},fe={class:"bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200"},_e={class:"p-5"},he={class:"flex items-center"},xe={class:"ml-5 w-0 flex-1"},pe={class:"text-lg font-medium text-gray-900"},ye={class:"bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200"},we={class:"p-5"},be={class:"flex items-center"},ke={class:"flex-shrink-0"},Ce={class:"ml-5 w-0 flex-1"},Pe={class:"text-lg font-medium text-gray-900"},Ae={class:"bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200"},Me={class:"p-5"},De={class:"flex items-center"},ze={class:"flex-shrink-0"},Re={class:"ml-5 w-0 flex-1"},Se={class:"text-lg font-medium text-gray-900"},$e={class:"font-medium text-blue-600"},Ee={class:"text-sm text-gray-900"},Fe={class:"text-sm font-medium text-green-600"},Ie={class:"text-sm font-medium text-red-600"},Le={class:"text-sm"},je={class:"font-medium text-gray-900"},Ve={key:0,class:"relative"},Be=["onClick"],Ne={key:0,class:"absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-10"},Oe={class:"py-1"},He=["onClick"],Te=["onClick"],Qe=["onClick"],Ue=["onClick"],Ye=W({__name:"LoanProducts",setup(M){const f=X(),c=a(!1),n=a([]),_=a(1),h=a(0),b=a(10),k=a(""),D=a(""),z=a("asc"),R=a(!1),l=Y({});a({product_name:"",repayment_period:12,interest_rate:0,late_fine_interest_rate:0,minimum_loan_amount:0,maximum_loan_amount:0});const S={product_name:"Product Name",repayment_period:"Period",interest_rate:"Interest Rate",late_fine_interest_rate:"Late Fine Rate",minimum_loan_amount:"Loan Amount Range",status:"Status"},$=g(()=>f.selectedClient),E=g(()=>f.hasPermission("loan_products_create")),F=g(()=>f.hasPermission("loan_products_edit")),I=g(()=>n.value.filter(o=>o.status===1).length),L=g(()=>n.value.length===0?0:(n.value.reduce((t,i)=>t+i.interest_rate,0)/n.value.length).toFixed(2)),j=g(()=>{if(n.value.length===0)return 0;const o=n.value.reduce((t,i)=>t+i.repayment_period,0);return Math.round(o/n.value.length)}),u=async(o={})=>{var t,i;c.value=!0;try{const s=await K.getLoanProducts({page:_.value,limit:b.value,search:k.value,client_id:f.selectedClientId,...o});s.status===200?(n.value=((t=s.message)==null?void 0:t.data)||[],h.value=((i=s.message)==null?void 0:i.total_count)||0):(n.value=[],h.value=0)}catch(s){console.error("Error fetching loan products:",s),n.value=[],h.value=0}finally{c.value=!1}},V=o=>{_.value=o,u()},B=o=>{k.value=o,_.value=1,u()},N=(o,t)=>{D.value=o,z.value=t,_.value=1,u()},O=o=>{console.log("Row clicked:",o)},H=()=>{u()},T=o=>{Object.keys(l).forEach(t=>{parseInt(t)!==o&&(l[parseInt(t)]=!1)}),l[o]=!l[o]},Q=o=>{console.log("Edit product:",o.product_name),l[n.value.indexOf(o)]=!1},U=o=>{console.log("View product details:",o.product_name),l[n.value.indexOf(o)]=!1},q=async o=>{try{c.value=!0,console.log("Activating product:",o.product_name),await u()}catch(t){console.error("Error activating product:",t)}finally{c.value=!1,l[n.value.indexOf(o)]=!1}},G=async o=>{try{c.value=!0,console.log("Deactivating product:",o.product_name),await u()}catch(t){console.error("Error deactivating product:",t)}finally{c.value=!1,l[n.value.indexOf(o)]=!1}},C=o=>new Intl.NumberFormat("en-US",{minimumFractionDigits:0,maximumFractionDigits:0}).format(o);return Z(()=>{u()}),(o,t)=>{var i;return v(),m("div",re,[e("div",ae,[e("div",null,[t[1]||(t[1]=e("h1",{class:"text-2xl font-bold text-gray-900"},"Loan Products",-1)),e("p",le," Manage loan products and configurations for "+r(((i=$.value)==null?void 0:i.client_name)||"All Organizations"),1)]),E.value?(v(),m("button",{key:0,onClick:t[0]||(t[0]=s=>R.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[x(y(te),{class:"h-4 w-4 mr-2"}),t[2]||(t[2]=A(" Add Loan Product "))])):w("",!0)]),e("div",ie,[e("div",de,[e("div",ce,[e("div",ue,[e("div",me,[x(y(oe),{class:"h-6 w-6 text-gray-400"})]),e("div",ve,[e("dl",null,[t[3]||(t[3]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Products",-1)),e("dd",ge,r(h.value),1)])])])])]),e("div",fe,[e("div",_e,[e("div",he,[t[5]||(t[5]=e("div",{class:"flex-shrink-0"},[e("div",{class:"h-6 w-6 bg-green-100 rounded-full flex items-center justify-center"},[e("div",{class:"h-3 w-3 bg-green-500 rounded-full"})])],-1)),e("div",xe,[e("dl",null,[t[4]||(t[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Active Products",-1)),e("dd",pe,r(I.value),1)])])])])]),e("div",ye,[e("div",we,[e("div",be,[e("div",ke,[x(y(se),{class:"h-6 w-6 text-gray-400"})]),e("div",Ce,[e("dl",null,[t[6]||(t[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Avg Interest Rate",-1)),e("dd",Pe,r(L.value)+"%",1)])])])])]),e("div",Ae,[e("div",Me,[e("div",De,[e("div",ze,[x(y(ne),{class:"h-6 w-6 text-gray-400"})]),e("div",Re,[e("dl",null,[t[7]||(t[7]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Avg Period",-1)),e("dd",Se,r(j.value)+" months",1)])])])])])]),x(J,{data:n.value,headers:S,loading:c.value,"current-page":_.value,"total-records":h.value,"page-size":b.value,title:"Loan Products","row-key":"client_product_id","has-actions":!0,onPageChange:V,onSearch:B,onSort:N,onRowClick:O},{"header-actions":d(()=>[e("button",{onClick:H,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},t[8]||(t[8]=[e("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),A(" Refresh ")]))]),"cell-product_name":d(({value:s})=>[e("div",$e,r(s),1)]),"cell-repayment_period":d(({value:s})=>[e("div",Ee,r(s)+" months",1)]),"cell-interest_rate":d(({value:s})=>[e("div",Fe,r(s)+"%",1)]),"cell-late_fine_interest_rate":d(({value:s})=>[e("div",Ie,r((parseFloat(s)*100).toFixed(2))+"%",1)]),"cell-minimum_loan_amount":d(({item:s})=>[e("div",Le,[e("div",je,r(s.currency_code)+" "+r(C(s.minimum_loan_amount))+" - "+r(C(s.maximum_loan_amount)),1),t[9]||(t[9]=e("div",{class:"text-xs text-gray-500"},"Min - Max Amount",-1))])]),"cell-status":d(({value:s})=>[e("span",{class:ee(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s===1?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},r(s===1?"Active":"Inactive"),3)]),actions:d(({item:s,index:P})=>[F.value?(v(),m("div",Ve,[e("button",{onClick:p=>T(P),class:"inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[10]||(t[10]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})],-1)]),8,Be),l[P]?(v(),m("div",Ne,[e("div",Oe,[e("button",{onClick:p=>Q(s),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"}," Edit Product ",8,He),e("button",{onClick:p=>U(s),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"}," View Details ",8,Te),s.status===1?(v(),m("button",{key:0,onClick:p=>G(s),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"}," Deactivate ",8,Qe)):(v(),m("button",{key:1,onClick:p=>q(s),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"}," Activate ",8,Ue))])])):w("",!0)])):w("",!0)]),_:1},8,["data","loading","current-page","total-records","page-size"])])}}});export{Ye as default};
