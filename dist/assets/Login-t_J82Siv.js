import{d as V,u as B,r as d,a as M,w as b,c as i,b as e,e as L,f as c,g as j,t as m,h,v as x,i as _,j as T,k as q,l as z,F as E,m as N,n as U,o as l}from"./index-DOaBqVmr.js";const D="/assets/logo-DxbWToUJ.png",F={class:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8"},R={class:"max-w-md w-full space-y-8"},A={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},P={key:0,class:"mb-4 p-4 rounded-md bg-red-50 border border-red-200"},H={class:"flex"},I={class:"ml-3"},O={class:"text-sm text-red-800"},W={key:1,class:"mb-4 p-4 rounded-md bg-green-50 border border-green-200"},J={class:"flex"},$={class:"ml-3"},G={class:"text-sm text-green-800"},K={class:"space-y-4"},Q={key:0},X={class:"mt-6"},Y=["disabled"],Z={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ee={class:"mt-4 text-center"},se={key:0,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},te={class:"space-y-2"},oe=["onClick"],re={class:"font-medium text-gray-900"},ne={class:"text-sm text-gray-500"},de=V({__name:"Login",setup(ae){const g=U(),v=B(),u=d(!1),n=d(""),a=d(""),f=d(!1),y=d(!1),w=d([]),o=M({username:"",password:"",verification_code:"",dial_code:"254"}),k=async()=>{n.value="",a.value="",u.value=!0;try{if(f.value){const t=await v.loginWithCode(o);t.success?t.requiresClientSelection&&t.clients?(y.value=!0,w.value=t.clients,a.value="Please select an organization to continue"):(a.value="Login successful! Redirecting...",setTimeout(()=>{g.push({name:"dashboard"})},1e3)):n.value=t.message||"Login failed"}else{const t=await v.login(o);t.success?(a.value="Login successful! Redirecting...",setTimeout(()=>{g.push({name:"dashboard"})},1e3)):t.requiresCode?(f.value=!0,a.value="Verification code sent. Please check your phone."):n.value=t.message||"Login failed"}}catch(t){n.value=t.message||"An unexpected error occurred"}finally{u.value=!1}},C=async t=>{try{await v.selectClient(t),a.value="Organization selected! Redirecting...",setTimeout(()=>{g.push({name:"dashboard"})},1e3)}catch(s){n.value=s.message||"Failed to select organization"}},p=()=>{n.value&&(n.value="")};return b(()=>o.username,p),b(()=>o.password,p),b(()=>o.verification_code,p),(t,s)=>{const S=z("router-link");return l(),i("div",F,[e("div",R,[s[11]||(s[11]=L('<div class="text-center"><div class="mx-auto h-20 w-auto flex items-center justify-center"><img src="'+D+'" alt="Mossbets B2B Logo" class="h-20 w-auto"></div><h2 class="mt-6 text-3xl font-extrabold text-gray-900"> Sign in to Mossbets B2B </h2><p class="mt-2 text-sm text-gray-600"> Business Dashboard </p></div>',1)),e("form",{class:"mt-8 space-y-6",onSubmit:j(k,["prevent"])},[e("div",A,[n.value?(l(),i("div",P,[e("div",H,[s[3]||(s[3]=e("svg",{class:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",I,[e("p",O,m(n.value),1)])])])):c("",!0),a.value?(l(),i("div",W,[e("div",J,[s[4]||(s[4]=e("svg",{class:"h-5 w-5 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)),e("div",$,[e("p",G,m(a.value),1)])])])):c("",!0),e("div",K,[e("div",null,[s[5]||(s[5]=e("label",{for:"username",class:"block text-sm font-medium text-gray-700"}," Username ",-1)),h(e("input",{id:"username","onUpdate:modelValue":s[0]||(s[0]=r=>o.username=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your username"},null,512),[[x,o.username]])]),e("div",null,[s[6]||(s[6]=e("label",{for:"password",class:"block text-sm font-medium text-gray-700"}," Password ",-1)),h(e("input",{id:"password","onUpdate:modelValue":s[1]||(s[1]=r=>o.password=r),type:"password",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your password"},null,512),[[x,o.password]])]),f.value?(l(),i("div",Q,[s[7]||(s[7]=e("label",{for:"verification_code",class:"block text-sm font-medium text-gray-700"}," Verification Code ",-1)),h(e("input",{id:"verification_code","onUpdate:modelValue":s[2]||(s[2]=r=>o.verification_code=r),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter verification code"},null,512),[[x,o.verification_code]])])):c("",!0)]),e("div",X,[e("button",{type:"submit",disabled:u.value,class:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"},[u.value?(l(),i("svg",Z,s[8]||(s[8]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):c("",!0),_(" "+m(u.value?"Signing in...":f.value?"Verify & Sign In":"Sign In"),1)],8,Y)]),e("div",ee,[T(S,{to:{name:"forgot-password"},class:"text-sm text-blue-600 hover:text-blue-500 transition-colors duration-200"},{default:q(()=>s[9]||(s[9]=[_(" Forgot your password? ")])),_:1,__:[9]})])])],32),y.value?(l(),i("div",se,[s[10]||(s[10]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Select Organization",-1)),e("div",te,[(l(!0),i(E,null,N(w.value,r=>(l(),i("button",{key:r.client_id,onClick:ie=>C(r.client_id),class:"w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200"},[e("div",re,m(r.client_name),1),e("div",ne,m(r.client_account),1)],8,oe))),128))])])):c("",!0)])])}}});export{de as default};
