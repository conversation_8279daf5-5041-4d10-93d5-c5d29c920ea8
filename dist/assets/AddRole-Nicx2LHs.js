import{d as B,r as p,x as $,p as j,c as a,b as e,j as D,i as T,y,g as U,h as c,v as h,C as F,F as b,m as g,t as l,n as O,f as I,D as L,o as d}from"./index-DOaBqVmr.js";import{s as _}from"./systemApi-CvVfCghC.js";import{R as q}from"./permissions-vZl7iLZK.js";import{r as z}from"./ArrowLeftIcon-DKfvydGS.js";const P={class:"space-y-6"},G={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},H={class:"flex items-center justify-between"},J={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},K={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Q={class:"mt-4"},W={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},X=["onClick"],Y={class:"font-medium text-gray-900"},Z={class:"text-sm text-gray-500 mt-1"},ee={class:"mt-2"},se={class:"text-xs text-blue-600"},te={class:"bg-gray-50 rounded-lg p-4"},oe={class:"flex items-center justify-between mb-4"},re={class:"text-sm font-medium text-gray-700"},le={class:"max-h-80 overflow-y-auto space-y-4"},ne={class:"px-3 py-2 bg-white border-b border-gray-200 rounded-t-lg"},ie={class:"flex items-center"},ae=["id","checked","onChange"],de=["for"],ue={class:"ml-auto text-xs text-gray-500"},ce={class:"px-3 py-2 space-y-2"},me=["id","value"],pe=["for"],be={key:0,class:"text-xs text-gray-400 ml-2"},ge={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200"},fe=["disabled"],we=B({__name:"AddRole",setup(xe){const f=O(),u=p(!1),m=p([]),r=p({role_name:"",description:"",permissions:[],status:1}),w=Object.values(q).map(t=>({id:t.id,name:t.name,description:t.description,permissions:t.permissions})),k=$(()=>{const t={};return m.value.forEach(s=>{const o=s.module||"general";t[o]||(t[o]=[]),t[o].push(s)}),t}),C=async()=>{var t;try{const s=await _.getPermissions({limit:1e3});s.status===200&&(m.value=((t=s.message)==null?void 0:t.data)||[])}catch(s){console.error("Error fetching permissions:",s)}},R=t=>{r.value.role_name=t.name,r.value.description=t.description,r.value.permissions=[...t.permissions]},x=t=>t.every(s=>r.value.permissions.includes(s.id)),A=t=>{x(t)?t.forEach(o=>{const n=r.value.permissions.indexOf(o.id);n>-1&&r.value.permissions.splice(n,1)}):t.forEach(o=>{r.value.permissions.includes(o.id)||r.value.permissions.push(o.id)})},S=()=>{r.value.permissions=m.value.map(t=>t.id)},E=()=>{r.value.permissions=[]},M=async()=>{u.value=!0;try{const t=await _.createRole({role_name:r.value.role_name,description:r.value.description,permissions:r.value.permissions});t.status===200?f.push({name:"system-roles"}):console.error("Failed to create role:",t.message)}catch(t){console.error("Error creating role:",t)}finally{u.value=!1}},v=()=>{f.push({name:"system-roles"})},V=t=>t.charAt(0).toUpperCase()+t.slice(1).replace(/[_-]/g," ");return j(()=>{C()}),(t,s)=>(d(),a("div",P,[e("div",G,[e("div",H,[s[5]||(s[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Add New Role"),e("p",{class:"text-gray-600 mt-1"},"Create a new system role with specific permissions")],-1)),e("button",{onClick:v,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[D(y(z),{class:"w-4 h-4 mr-2"}),s[4]||(s[4]=T(" Back to Roles "))])])]),e("div",J,[e("form",{onSubmit:U(M,["prevent"]),class:"space-y-6"},[e("div",null,[s[10]||(s[10]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Basic Information",-1)),e("div",K,[e("div",null,[s[6]||(s[6]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Role Name *",-1)),c(e("input",{"onUpdate:modelValue":s[0]||(s[0]=o=>r.value.role_name=o),type:"text",required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter role name"},null,512),[[h,r.value.role_name]])]),e("div",null,[s[8]||(s[8]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Status",-1)),c(e("select",{"onUpdate:modelValue":s[1]||(s[1]=o=>r.value.status=o),class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},s[7]||(s[7]=[e("option",{value:1},"Active",-1),e("option",{value:0},"Inactive",-1)]),512),[[F,r.value.status]])])]),e("div",Q,[s[9]||(s[9]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Description",-1)),c(e("textarea",{"onUpdate:modelValue":s[2]||(s[2]=o=>r.value.description=o),rows:"3",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Describe the role and its purpose"},null,512),[[h,r.value.description]])])]),e("div",null,[s[11]||(s[11]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Role Templates",-1)),e("div",W,[(d(!0),a(b,null,g(y(w),o=>(d(),a("div",{key:o.id,onClick:n=>R(o),class:"border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200"},[e("h4",Y,l(o.name),1),e("p",Z,l(o.description),1),e("div",ee,[e("span",se,l(o.permissions.length)+" permissions",1)])],8,X))),128))])]),e("div",null,[s[12]||(s[12]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Permissions",-1)),e("div",te,[e("div",oe,[e("span",re,l(r.value.permissions.length)+" permissions selected ",1),e("div",{class:"flex space-x-2"},[e("button",{type:"button",onClick:S,class:"text-sm text-blue-600 hover:text-blue-800"}," Select All "),e("button",{type:"button",onClick:E,class:"text-sm text-gray-600 hover:text-gray-800"}," Clear All ")])]),e("div",le,[(d(!0),a(b,null,g(k.value,(o,n)=>(d(),a("div",{key:n,class:"border border-gray-200 rounded-lg"},[e("div",ne,[e("div",ie,[e("input",{id:`module-${n}`,type:"checkbox",checked:x(o),onChange:i=>A(o),class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,40,ae),e("label",{for:`module-${n}`,class:"ml-2 text-sm font-medium text-gray-900 capitalize"},l(V(n)),9,de),e("span",ue,l(o.length)+" permissions ",1)])]),e("div",ce,[(d(!0),a(b,null,g(o,i=>(d(),a("div",{key:i.id,class:"flex items-center"},[c(e("input",{id:`permission-${i.id}`,"onUpdate:modelValue":s[3]||(s[3]=N=>r.value.permissions=N),value:i.id,type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,8,me),[[L,r.value.permissions]]),e("label",{for:`permission-${i.id}`,class:"ml-2 text-sm text-gray-700 flex-1"},l(i.name),9,pe),i.description?(d(),a("span",be,l(i.description),1)):I("",!0)]))),128))])]))),128))])])]),e("div",ge,[e("button",{type:"button",onClick:v,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Cancel "),e("button",{type:"submit",disabled:u.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},l(u.value?"Creating...":"Create Role"),9,fe)])],32)])]))}});export{we as default};
