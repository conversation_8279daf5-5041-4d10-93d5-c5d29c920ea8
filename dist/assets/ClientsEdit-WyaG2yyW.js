import{d as A,r as _,a as w,p as B,c as d,b as t,j as f,t as n,k,l as M,e as P,h as i,f as y,v as u,z as X,C as p,F as C,m as h,i as x,g as I,q,n as j,y as z,o as a}from"./index-DOaBqVmr.js";import{c as S}from"./clientsApi-DPSRLazl.js";import{r as K}from"./ArrowLeftIcon-DKfvydGS.js";const L={class:"space-y-6"},O={class:"flex items-center justify-between"},R={class:"mt-1 text-sm text-gray-500"},T={key:0,class:"bg-white shadow-sm rounded-lg border border-gray-200 p-6"},H={key:1,class:"bg-white shadow-sm rounded-lg border border-gray-200"},$={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Y={key:0,class:"mt-1 text-sm text-red-600"},G={key:0,class:"mt-1 text-sm text-red-600"},J={key:0,class:"mt-1 text-sm text-red-600"},Q={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},W={class:"mt-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900"},Z={class:"mt-1"},ee={class:"mt-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-900"},te={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},le={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},se=["value"],oe=["value"],re={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},ne={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200"},ie=["disabled"],de={key:0,class:"flex items-center"},ae={key:1},pe=A({__name:"ClientsEdit",setup(ue){const c=j(),U=q(),b=_(!1),g=_(!0),l=w({client_id:"",client_account:"",client_name:"",client_email:"",client_phone:"",client_address:"",can_issue_loans:"1",service_fee:"0.030",currency_code:"KES",open_date:"1",close_date:"31",b2c_paybill:"3037395",c2b_paybill:"4114763",sender_id:"SaloPlus",client_status:"1",total_loan_assets:"0"}),r=w({}),V=async()=>{const o=U.params.id;if(!o){c.push({name:"clients"});return}g.value=!0;try{const e=await S.getClient(o);e.status===200&&e.message?Object.assign(l,e.message):(console.error("Failed to fetch client:",e.message),c.push({name:"clients"}))}catch(e){console.error("Error fetching client:",e),c.push({name:"clients"})}finally{g.value=!1}},E=()=>{var e,m,s;Object.keys(r).forEach(N=>delete r[N]);let o=!0;return(e=l.client_name)!=null&&e.trim()||(r.client_name="Client name is required",o=!1),(m=l.client_email)!=null&&m.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(l.client_email)||(r.client_email="Please enter a valid email address",o=!1):(r.client_email="Email address is required",o=!1),(s=l.client_phone)!=null&&s.trim()?/^254\d{9}$/.test(l.client_phone.replace(/\s+/g,""))||(r.client_phone="Please enter a valid Kenyan phone number (254XXXXXXXXX)",o=!1):(r.client_phone="Phone number is required",o=!1),o},D=async()=>{if(E()){b.value=!0;try{const o=await S.updateClient(l);o.status===200?c.push({name:"clients"}):console.error("Failed to update client:",o.message)}catch(o){console.error("Error updating client:",o)}finally{b.value=!1}}},F=o=>{const e=typeof o=="string"?parseFloat(o):o;return new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(e||0)},v=o=>{const e=parseInt(o);if(e>=11&&e<=13)return"th";switch(e%10){case 1:return"st";case 2:return"nd";case 3:return"rd";default:return"th"}};return B(()=>{V()}),(o,e)=>{const m=M("router-link");return a(),d("div",L,[t("div",O,[t("div",null,[e[12]||(e[12]=t("h1",{class:"text-2xl font-bold text-gray-900"},"Edit Client",-1)),t("p",R," Update client information for "+n(l.client_name||"Loading..."),1)]),f(m,{to:{name:"clients"},class:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},{default:k(()=>[f(z(K),{class:"h-4 w-4 mr-2"}),e[13]||(e[13]=x(" Back to Clients "))]),_:1,__:[13]})]),g.value?(a(),d("div",T,e[14]||(e[14]=[P('<div class="animate-pulse"><div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div><div class="space-y-3"><div class="h-4 bg-gray-200 rounded"></div><div class="h-4 bg-gray-200 rounded w-5/6"></div><div class="h-4 bg-gray-200 rounded w-4/6"></div></div></div>',1)]))):(a(),d("div",H,[t("form",{onSubmit:I(D,["prevent"]),class:"space-y-6 p-6"},[t("div",null,[e[19]||(e[19]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Basic Information",-1)),t("div",$,[t("div",null,[e[15]||(e[15]=t("label",{for:"client_name",class:"block text-sm font-medium text-gray-700"},"Client Name *",-1)),i(t("input",{id:"client_name","onUpdate:modelValue":e[0]||(e[0]=s=>l.client_name=s),type:"text",required:"",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter client organization name"},null,512),[[u,l.client_name]]),r.client_name?(a(),d("p",Y,n(r.client_name),1)):y("",!0)]),t("div",null,[e[16]||(e[16]=t("label",{for:"client_email",class:"block text-sm font-medium text-gray-700"},"Email Address *",-1)),i(t("input",{id:"client_email","onUpdate:modelValue":e[1]||(e[1]=s=>l.client_email=s),type:"email",required:"",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter email address"},null,512),[[u,l.client_email]]),r.client_email?(a(),d("p",G,n(r.client_email),1)):y("",!0)]),t("div",null,[e[17]||(e[17]=t("label",{for:"client_phone",class:"block text-sm font-medium text-gray-700"},"Phone Number *",-1)),i(t("input",{id:"client_phone","onUpdate:modelValue":e[2]||(e[2]=s=>l.client_phone=s),type:"tel",required:"",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"254712345678"},null,512),[[u,l.client_phone]]),r.client_phone?(a(),d("p",J,n(r.client_phone),1)):y("",!0)]),t("div",null,[e[18]||(e[18]=t("label",{for:"client_address",class:"block text-sm font-medium text-gray-700"},"Address",-1)),i(t("input",{id:"client_address","onUpdate:modelValue":e[3]||(e[3]=s=>l.client_address=s),type:"text",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter physical address"},null,512),[[u,l.client_address]])])])]),t("div",null,[e[23]||(e[23]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Account Information",-1)),t("div",Q,[t("div",null,[e[20]||(e[20]=t("label",{class:"block text-sm font-medium text-gray-700"},"Client Account",-1)),t("div",W,n(l.client_account||"N/A"),1)]),t("div",null,[e[21]||(e[21]=t("label",{class:"block text-sm font-medium text-gray-700"},"Client Status",-1)),t("div",Z,[t("span",{class:X(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",l.client_status==="1"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},n(l.client_status==="1"?"Active":"Inactive"),3)])]),t("div",null,[e[22]||(e[22]=t("label",{class:"block text-sm font-medium text-gray-700"},"Total Loan Assets",-1)),t("div",ee,n(l.currency_code)+" "+n(F(l.total_loan_assets||"0")),1)])])]),t("div",null,[e[30]||(e[30]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Loan Configuration",-1)),t("div",te,[t("div",null,[e[25]||(e[25]=t("label",{for:"can_issue_loans",class:"block text-sm font-medium text-gray-700"},"Can Issue Loans",-1)),i(t("select",{id:"can_issue_loans","onUpdate:modelValue":e[4]||(e[4]=s=>l.can_issue_loans=s),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},e[24]||(e[24]=[t("option",{value:"1"},"Yes",-1),t("option",{value:"0"},"No",-1)]),512),[[p,l.can_issue_loans]])]),t("div",null,[e[26]||(e[26]=t("label",{for:"service_fee",class:"block text-sm font-medium text-gray-700"},"Service Fee (%)",-1)),i(t("input",{id:"service_fee","onUpdate:modelValue":e[5]||(e[5]=s=>l.service_fee=s),type:"number",step:"0.001",min:"0",max:"1",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"0.030"},null,512),[[u,l.service_fee]]),e[27]||(e[27]=t("p",{class:"mt-1 text-xs text-gray-500"},"Enter as decimal (e.g., 0.030 for 3%)",-1))]),t("div",null,[e[29]||(e[29]=t("label",{for:"currency_code",class:"block text-sm font-medium text-gray-700"},"Currency",-1)),i(t("select",{id:"currency_code","onUpdate:modelValue":e[6]||(e[6]=s=>l.currency_code=s),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},e[28]||(e[28]=[t("option",{value:"KES"},"KES - Kenyan Shilling",-1),t("option",{value:"USD"},"USD - US Dollar",-1),t("option",{value:"EUR"},"EUR - Euro",-1)]),512),[[p,l.currency_code]])])])]),t("div",null,[e[33]||(e[33]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Operating Hours",-1)),t("div",le,[t("div",null,[e[31]||(e[31]=t("label",{for:"open_date",class:"block text-sm font-medium text-gray-700"},"Opening Day of Month",-1)),i(t("select",{id:"open_date","onUpdate:modelValue":e[7]||(e[7]=s=>l.open_date=s),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},[(a(),d(C,null,h(31,s=>t("option",{key:s,value:s.toString()},n(s)+n(v(s.toString())),9,se)),64))],512),[[p,l.open_date]])]),t("div",null,[e[32]||(e[32]=t("label",{for:"close_date",class:"block text-sm font-medium text-gray-700"},"Closing Day of Month",-1)),i(t("select",{id:"close_date","onUpdate:modelValue":e[8]||(e[8]=s=>l.close_date=s),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},[(a(),d(C,null,h(31,s=>t("option",{key:s,value:s.toString()},n(s)+n(v(s.toString())),9,oe)),64))],512),[[p,l.close_date]])])])]),t("div",null,[e[38]||(e[38]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Payment Configuration",-1)),t("div",re,[t("div",null,[e[34]||(e[34]=t("label",{for:"b2c_paybill",class:"block text-sm font-medium text-gray-700"},"B2C PayBill",-1)),i(t("input",{id:"b2c_paybill","onUpdate:modelValue":e[9]||(e[9]=s=>l.b2c_paybill=s),type:"text",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"3037395"},null,512),[[u,l.b2c_paybill]])]),t("div",null,[e[35]||(e[35]=t("label",{for:"c2b_paybill",class:"block text-sm font-medium text-gray-700"},"C2B PayBill",-1)),i(t("input",{id:"c2b_paybill","onUpdate:modelValue":e[10]||(e[10]=s=>l.c2b_paybill=s),type:"text",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"4114763"},null,512),[[u,l.c2b_paybill]])]),t("div",null,[e[36]||(e[36]=t("label",{for:"sender_id",class:"block text-sm font-medium text-gray-700"},"SMS Sender ID",-1)),i(t("input",{id:"sender_id","onUpdate:modelValue":e[11]||(e[11]=s=>l.sender_id=s),type:"text",maxlength:"11",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"SaloPlus"},null,512),[[u,l.sender_id]]),e[37]||(e[37]=t("p",{class:"mt-1 text-xs text-gray-500"},"Maximum 11 characters",-1))])])]),t("div",ne,[f(m,{to:{name:"clients"},class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"},{default:k(()=>e[39]||(e[39]=[x(" Cancel ")])),_:1,__:[39]}),t("button",{type:"submit",disabled:b.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"},[b.value?(a(),d("span",de,e[40]||(e[40]=[t("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),x(" Updating... ")]))):(a(),d("span",ae,"Update Client"))],8,ie)])],32)]))])}}});export{pe as default};
