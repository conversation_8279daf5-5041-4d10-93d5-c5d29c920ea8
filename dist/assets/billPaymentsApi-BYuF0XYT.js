import{K as i,G as d}from"./index-DOaBqVmr.js";const _={async getBillPayments(e={}){var r,n;try{const s={limit:e.limit||10,offset:e.offset||1,page:e.page||1,start:e.start||"",end:e.end||"",utility_id:e.utility_id||"",utility_name:e.utility_name||"",sort:"",export:"",...e},a=i(s),t=(await d.post("bill_payment/v1/view/utilities",s,{headers:{"X-Hash-Key":a}})).data.data;return{status:t.code===200?200:t.code,message:{data:t.data||[],total_count:t.total_count||0,current_page:t.current_page||1,per_page:t.per_page||s.limit},code:t.code.toString()}}catch(s){if(console.error("Error fetching bill payments:",s),s.response){const a=s.response.status,o=s.response.data;return{status:((r=o.data)==null?void 0:r.code)||a,message:{data:[],total_count:0,current_page:1,per_page:e.limit||10},code:(((n=o.data)==null?void 0:n.code)||a).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:e.limit||10},code:"500"}}},async getBillPaymentTransactions(e={}){var r,n;try{const s={offset:e.offset||1,limit:e.limit||10,start:e.start||"",end:e.end||"",sort:"",export:"",utility_id:e.utility_id||"",loan_number:e.loan_number||"",client_id:e.client_id||"",status:e.status||"",reference_number:e.reference_number||"",receipt_number:e.receipt_number||"",client_phone:e.client_phone||"",...e},a=i(s),t=(await d.post("bill_payment/v1/view/trxn",s,{headers:{"X-Hash-Key":a}})).data.data;return{status:t.code===200?200:t.code,message:{data:t.data||[],total_count:t.total_count||0,current_page:t.current_page||1,per_page:t.per_page||s.limit},code:t.code.toString()}}catch(s){if(console.error("Error fetching bill payment transactions:",s),s.response){const a=s.response.status,o=s.response.data;return{status:((r=o.data)==null?void 0:r.code)||a,message:{data:[],total_count:0,current_page:1,per_page:e.limit||10},code:(((n=o.data)==null?void 0:n.code)||a).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:e.limit||10},code:"500"}}},async createBillPayment(e){var r,n,s;try{const a={utility_name:e.utility_name,utility_desc:e.utility_desc,utility_icon:e.utility_icon||"",user_name:e.user_name||"",user_pass:e.user_pass||"",api_key:e.api_key||""},o=i(a),c=(await d.post("bill_payment/v1/create",a,{headers:{"X-Hash-Key":o}})).data.data;return{status:c.code,message:c.message||c,code:c.code.toString()}}catch(a){if(console.error("Error creating bill payment:",a),a.response){const o=a.response.status,t=a.response.data;return{status:((r=t.data)==null?void 0:r.code)||o,message:((n=t.data)==null?void 0:n.message)||"Failed to create bill payment",code:(((s=t.data)==null?void 0:s.code)||o).toString()}}return{status:500,message:"Failed to create bill payment",code:"500"}}},async updateBillPayment(e,r){var n,s,a;try{const o={id:e,...r},t=i(o),l=(await d.post("bill_payment/v1/update",o,{headers:{"X-Hash-Key":t}})).data.data;return{status:l.code,message:l.message||l,code:l.code.toString()}}catch(o){if(console.error("Error updating bill payment:",o),o.response){const t=o.response.status,c=o.response.data;return{status:((n=c.data)==null?void 0:n.code)||t,message:((s=c.data)==null?void 0:s.message)||"Failed to update bill payment",code:(((a=c.data)==null?void 0:a.code)||t).toString()}}return{status:500,message:"Failed to update bill payment",code:"500"}}},async deleteBillPayment(e){var r,n,s;try{const a={id:e},o=i(a),c=(await d.post("bill_payment/v1/delete",a,{headers:{"X-Hash-Key":o}})).data.data;return{status:c.code,message:c.message||c,code:c.code.toString()}}catch(a){if(console.error("Error deleting bill payment:",a),a.response){const o=a.response.status,t=a.response.data;return{status:((r=t.data)==null?void 0:r.code)||o,message:((n=t.data)==null?void 0:n.message)||"Failed to delete bill payment",code:(((s=t.data)==null?void 0:s.code)||o).toString()}}return{status:500,message:"Failed to delete bill payment",code:"500"}}},async getWithdrawals(e={}){var r,n;try{const s={offset:e.offset||1,limit:e.limit||10,start:e.start||"",end:e.end||"",sort:"",export:"",amount:"",reference_number:e.reference_number||"",withdrawal_status:e.status||"",receipt_number:e.receipt_number||"",loan_number:e.loan_number||"",client_id:e.client_id||"",client_phone:e.client_phone||"",...e},a=i(s),t=(await d.post("bill_payment/v1/view/withdrawals",s,{headers:{"X-Hash-Key":a}})).data.data;return{status:t.code===200?200:t.code,message:{data:t.data||[],total_count:t.total_count||0,current_page:t.current_page||1,per_page:t.per_page||s.limit},code:t.code.toString()}}catch(s){if(console.error("Error fetching withdrawals:",s),s.response){const a=s.response.status,o=s.response.data;return{status:((r=o.data)==null?void 0:r.code)||a,message:{data:[],total_count:0,current_page:1,per_page:e.limit||10},code:(((n=o.data)==null?void 0:n.code)||a).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:e.limit||10},code:"500"}}},async getTransactions(e={}){var r,n;try{const s={offset:e.offset||1,limit:e.limit||10,sort:"",export:"",start:e.start||"",end:e.end||"",source:e.source||"",amount:e.amount||"",client_id:e.client_id||"",loan_number:e.loan_number||"",client_phone:e.client_phone||"",client_email:e.client_email||"",channel_name:e.channel_name||"",reference_type_id:e.reference_type_id||"",trxn_reference_id:e.trxn_reference_id||"",...e},a=i(s),t=(await d.post("merchant/v1/view/transactions",s,{headers:{"X-Hash-Key":a}})).data.data;return{status:t.code===200?200:t.code,message:{data:t.data||[],total_count:t.total_count||0,current_page:t.current_page||1,per_page:t.per_page||s.limit},code:t.code.toString()}}catch(s){if(console.error("Error fetching transactions:",s),s.response){const a=s.response.status,o=s.response.data;return{status:((r=o.data)==null?void 0:r.code)||a,message:{data:[],total_count:0,current_page:1,per_page:e.limit||10},code:(((n=o.data)==null?void 0:n.code)||a).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:e.limit||10},code:"500"}}}};export{_ as b};
