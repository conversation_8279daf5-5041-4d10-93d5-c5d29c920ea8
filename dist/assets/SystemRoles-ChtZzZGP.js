import{s as y}from"./systemApi-CvVfCghC.js";import{_ as w}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{_ as k}from"./SearchFilter.vue_vue_type_script_setup_true_lang-jgd-6IIX.js";import{_,c as d,b as s,j as c,i,k as a,l as g,t as f,o as m}from"./index-DOaBqVmr.js";import"./MagnifyingGlassIcon-D7MUVaIi.js";import"./XMarkIcon-CqcEu60T.js";const v={components:{DataTable:w,SearchFilter:k},data(){return{isOpen:!1,isLoading:!1,items:[],fullPage:!0,total:0,limit:10,offset:1,permissions:[],searchFilters:{search:"",status:""},tableHeaders:{index:"#",role_name:"Role Name",status:"Status",created_at:"Date Created"}}},computed:{data(){return this.items}},mounted(){this.getItems()},methods:{gotToPage(e){let t=this;t.offset=e,t.getItems()},async editRole(e){this.$router.push({name:"edit-role",params:{id:e.role_id}})},async getItems(){var u;let e=this;e.isLoading=!0;let t={page:e.offset,per_page:e.limit};try{let l=await y.getRoles(t);l.status===200&&(e.items=((u=l.message)==null?void 0:u.data)||l.message||[],e.total=e.items.length)}catch(l){console.error("Error fetching roles:",l),e.items=[],e.total=0}e.isLoading=!1},handleSearch(e){this.searchFilters.search=e,this.applyFilters()},handleFilter(e){Object.assign(this.searchFilters,e),this.applyFilters()},handleClearFilters(){this.searchFilters={search:"",status:""},this.applyFilters()},applyFilters(){this.offset=1,this.getItems()},handleRowClick(e,t){console.log("Row clicked:",e,t)},handleLimitChange(e){this.limit=e,this.offset=1,this.getItems()},getRoleBg(e){switch(e=parseInt(e),e){case 1:return"bg-green-600";case 2:return"bg-orange-600";case 3:return"bg-amber-400";case 4:return"bg-teal-600";case 5:return"bg-sky-600";default:return"bg-purple-600"}},getStatusBg(e){switch(e=parseInt(e),e){case 1:return"bg-green-600";case 2:return"bg-orange-600";case 3:return"bg-red-600";default:return"bg-purple-600"}},async permissionList(e){this.permissions=e,this.isOpen=!0},formatDate(e){if(!e)return"N/A";try{return new Date(e).toLocaleString()}catch{return"N/A"}}}},C={class:"space-y-6"},F={class:"flex items-center justify-between"},R={class:"flex items-center space-x-3"},S={class:"text-gray-900 font-medium"},I={class:"text-gray-900 font-medium"},L={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},B={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800"},H={key:2,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800"},D={class:"text-gray-900"},j=["onClick"];function A(e,t,u,l,n,o){const h=g("router-link"),p=g("SearchFilter"),x=g("DataTable");return m(),d("div",C,[s("div",F,[t[5]||(t[5]=s("div",null,[s("h1",{class:"text-2xl font-bold text-gray-900"},"System Roles"),s("p",{class:"mt-1 text-sm text-gray-500"}," Manage system roles and their permissions ")],-1)),s("div",R,[s("button",{onClick:t[0]||(t[0]=(...r)=>o.getItems&&o.getItems(...r)),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[3]||(t[3]=[s("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),i(" Refresh ")])),c(h,{to:{name:"add-role"},class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},{default:a(()=>t[4]||(t[4]=[s("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),i(" Add Role ")])),_:1,__:[4]})])]),c(p,{filters:n.searchFilters,"onUpdate:filters":t[1]||(t[1]=r=>n.searchFilters=r),"search-placeholder":"Search roles by name or description...","show-status-filter":!0,"show-role-filter":!1,"show-module-filter":!1,"show-date-filter":!1,"show-client-filter":!1,"has-advanced-filters":!1,onSearch:o.handleSearch,onFilter:o.handleFilter,onClear:o.handleClearFilters},null,8,["filters","onSearch","onFilter","onClear"]),c(x,{data:o.data,headers:n.tableHeaders,title:"System Roles",loading:n.isLoading,searchable:!0,pagination:!0,"current-page":n.offset,"total-records":n.total,"page-size":n.limit,"has-actions":!0,"empty-message":"No roles found",onPageChange:o.gotToPage,onSearch:o.handleSearch,onRowClick:o.handleRowClick,onLimitChange:o.handleLimitChange},{"header-actions":a(()=>[s("button",{onClick:t[2]||(t[2]=(...r)=>o.getItems&&o.getItems(...r)),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[6]||(t[6]=[s("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),i(" Refresh ")])),c(h,{to:{name:"add-role"},class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},{default:a(()=>t[7]||(t[7]=[s("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),i(" Add Role ")])),_:1,__:[7]})]),"cell-index":a(({index:r})=>[s("span",S,f(r+1),1)]),"cell-role_name":a(({item:r})=>[s("span",I,f(r.role_name),1)]),"cell-status":a(({item:r})=>[parseInt(r.status)===1?(m(),d("span",L," Active ")):parseInt(r.status)===3?(m(),d("span",B," Deactivated ")):(m(),d("span",H," Inactive "))]),"cell-created_at":a(({item:r})=>[s("span",D,f(o.formatDate(r.created_at)),1)]),actions:a(({item:r,closeDropdown:b})=>[s("button",{onClick:N=>{o.editRole(r),b()},class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"},t[8]||(t[8]=[s("svg",{class:"inline w-4 h-4 mr-2 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),i(" Edit Role ")]),8,j)]),_:1},8,["data","headers","loading","current-page","total-records","page-size","onPageChange","onSearch","onRowClick","onLimitChange"])])}const V=_(v,[["render",A]]);export{V as default};
