import{d as q,u as G,r as l,a as J,p as K,c as g,b as e,j as d,i as h,y as p,k as i,l as W,h as v,C as A,F as X,m as Y,v as S,t as a,f as Z,n as ee,o as y}from"./index-DOaBqVmr.js";import{_ as te}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{b as B}from"./billPaymentsApi-BYuF0XYT.js";import{r as se}from"./ArrowPathIcon-BCh5HUKO.js";import{r as oe}from"./PlusIcon-BKTWa0k6.js";import{r as re}from"./EllipsisVerticalIcon-BCCOPEML.js";import{r as ne}from"./ArrowDownTrayIcon-BWjpub36.js";const le={class:"space-y-6"},ae={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},ie={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},ue={class:"mt-4 sm:mt-0 flex space-x-3"},ce={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},de={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},me=["value"],fe={class:"flex space-x-2"},be={class:"text-sm font-medium text-gray-900"},ge={class:"text-sm"},pe={class:"font-medium text-gray-900"},ve={class:"text-gray-500"},ye={class:"text-sm"},xe={class:"font-medium text-gray-900"},he={class:"text-gray-500"},_e={class:"text-sm font-medium text-gray-900"},we={class:"text-sm text-gray-900"},ke={class:"text-sm text-gray-900"},Ce={class:"relative"},De=["onClick"],Pe={key:0,class:"absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},Ae={class:"py-1"},Se=["onClick"],Be=["onClick"],je=q({__name:"BillPayments",setup(Ve){ee(),G();const x=l(!1),f=l([]),_=l([]),c=l(1),b=l(0),w=l(10),V=l(""),R=l(""),F=l("asc"),m=J({}),r=l({utility_id:"",status:"",start:"",end:"",reference_number:"",receipt_number:"",client_phone:""}),N=[{key:"reference_number",label:"Ref No",sortable:!0},{key:"customer",label:"Profile",sortable:!1},{key:"receipt_info",label:"Receipt No & Desc",sortable:!1},{key:"amount",label:"Amount",sortable:!0},{key:"discount",label:"Discount",sortable:!0},{key:"created",label:"Date",sortable:!0}],u=async()=>{var o,t;x.value=!0;try{const n=await B.getBillPaymentTransactions({page:c.value,limit:w.value,...r.value});n.status===200?(f.value=((o=n.message)==null?void 0:o.data)||[],b.value=((t=n.message)==null?void 0:t.total_count)||0):(f.value=[],b.value=0)}catch(n){console.error("Error fetching bill payment transactions:",n),f.value=[],b.value=0}finally{x.value=!1}},k=async()=>{var o;try{const t=await B.getBillPayments({limit:100});t.status===200&&(_.value=[{text:"All",value:""},...(((o=t.message)==null?void 0:o.data)||[]).map(n=>({text:n.bill_name,value:n.id}))])}catch(t){console.error("Error fetching utilities:",t)}},U=o=>{c.value=o,u()},E=o=>{V.value=o,c.value=1,u()},$=(o,t)=>{R.value=o,F.value=t,u()},T=o=>{C(o)},j=()=>{u(),k()},z=()=>{c.value=1,u()},M=()=>{r.value={utility_id:"",status:"",start:"",end:"",reference_number:"",receipt_number:"",client_phone:""},c.value=1,u()},I=o=>{Object.keys(m).forEach(t=>{parseInt(t)!==o&&(m[parseInt(t)]=!1)}),m[o]=!m[o]},C=o=>{console.log("View details for transaction:",o.reference_number)},L=o=>{console.log("Download receipt for transaction:",o.reference_number)},H=()=>{console.log("Export bill payment transactions data")},O=o=>(typeof o=="string"?parseFloat(o):o).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,","),Q=o=>{if(!o)return"N/A";try{return new Date(o).toLocaleString()}catch{return"N/A"}};return K(()=>{k(),u()}),(o,t)=>{const n=W("router-link");return y(),g("div",le,[e("div",ae,[e("div",ie,[t[6]||(t[6]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Bill Payments"),e("p",{class:"text-gray-600 mt-1"},"Manage bill payment transactions and utilities")],-1)),e("div",ue,[e("button",{onClick:j,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[d(p(se),{class:"w-4 h-4 mr-2"}),t[4]||(t[4]=h(" Refresh "))]),d(n,{to:{name:"add-bill-payment"},class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},{default:i(()=>[d(p(oe),{class:"w-4 h-4 mr-2"}),t[5]||(t[5]=h(" Add Utility "))]),_:1,__:[5]})])])]),e("div",ce,[e("div",de,[e("div",null,[t[8]||(t[8]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Utility",-1)),v(e("select",{"onUpdate:modelValue":t[0]||(t[0]=s=>r.value.utility_id=s),class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},[t[7]||(t[7]=e("option",{value:""},"All Utilities",-1)),(y(!0),g(X,null,Y(_.value,s=>(y(),g("option",{key:s.value,value:s.value},a(s.text),9,me))),128))],512),[[A,r.value.utility_id]])]),e("div",null,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Status",-1)),v(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>r.value.status=s),class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},t[9]||(t[9]=[e("option",{value:""},"All Status",-1),e("option",{value:"1"},"Active",-1),e("option",{value:"0"},"Inactive",-1)]),512),[[A,r.value.status]])]),e("div",null,[t[11]||(t[11]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Start Date",-1)),v(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>r.value.start=s),type:"date",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[S,r.value.start]])]),e("div",null,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"End Date",-1)),v(e("input",{"onUpdate:modelValue":t[3]||(t[3]=s=>r.value.end=s),type:"date",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[S,r.value.end]])])]),e("div",{class:"mt-4 flex justify-end space-x-3"},[e("button",{onClick:M,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Clear "),e("button",{onClick:z,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Apply Filters ")])]),d(te,{data:f.value,headers:N,loading:x.value,"current-page":c.value,"total-records":b.value,"page-size":w.value,title:"Bill Payment Transactions","row-key":"id","has-actions":!0,onPageChange:U,onSearch:E,onSort:$,onRowClick:T},{"header-actions":i(()=>[e("div",fe,[e("button",{onClick:H,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[d(p(ne),{class:"w-4 h-4 mr-2"}),t[13]||(t[13]=h(" Export "))])])]),"cell-reference_number":i(({row:s})=>[e("div",be,a(s.reference_number),1)]),"cell-customer":i(({row:s})=>[e("div",ge,[e("div",pe,"+"+a(s.customer?s.customer.split(" - ")[0]:""),1),e("div",ve,a(s.customer?s.customer.split(" - ")[1]:""),1)])]),"cell-receipt_info":i(({row:s})=>[e("div",ye,[e("div",xe,a(s.receipt_number),1),e("div",he,a(s.narration),1)])]),"cell-amount":i(({row:s})=>[e("div",_e,a(s.currency_code)+"."+a(O(s.amount)),1)]),"cell-discount":i(({row:s})=>[e("div",we,a(s.discount||"0"),1)]),"cell-created":i(({row:s})=>[e("div",ke,a(Q(s.created)),1)]),actions:i(({row:s,index:D})=>[e("div",Ce,[e("button",{onClick:P=>I(D),class:"inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[d(p(re),{class:"w-4 h-4"})],8,De),m[D]?(y(),g("div",Pe,[e("div",Ae,[e("button",{onClick:P=>C(s),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," View Details ",8,Se),e("button",{onClick:P=>L(s),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-900"}," Download Receipt ",8,Be)])])):Z("",!0)])]),_:1},8,["data","loading","current-page","total-records","page-size"])])}}});export{je as default};
