import{d as G,x as Be,c as d,j as l,k as i,y as h,T as Ee,f as _,g as je,b as t,F as Q,m as X,z as A,B as I,i as c,E as Z,t as o,o as r,r as b,p as Se,q as Fe,n as Ie}from"./index-DOaBqVmr.js";import{_ as T,r as J,a as Pe,b as Ve,c as ze}from"./ActionButton.vue_vue_type_script_setup_true_lang-BwXxo59e.js";import{_ as S}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{r as Ke}from"./EllipsisVerticalIcon-BCCOPEML.js";import{a as Oe,r as z}from"./PencilIcon-Bgul0WAp.js";import{r as ee}from"./TrashIcon-D374yQ2i.js";import{r as K}from"./XMarkIcon-CqcEu60T.js";import{c as w}from"./clientsApi-DPSRLazl.js";import{r as He}from"./ArrowLeftIcon-DKfvydGS.js";import{r as Ue}from"./CreditCardIcon-Bp2xwmwY.js";import{r as W}from"./PlusIcon-BKTWa0k6.js";import"./UserGroupIcon-CvI7Mo90.js";import"./ArrowDownTrayIcon-BWjpub36.js";const Ye={class:"relative"},Me={class:"py-1"},Je=["onClick"],F=G({__name:"TableActions",props:{item:{},actions:{default:()=>[]},isOpen:{type:Boolean,default:!1}},emits:["toggle","action"],setup(O,{emit:P}){const v=O,q=P,m=Be(()=>v.actions.filter(p=>p.condition?p.condition(v.item):!0)),R=()=>{q("toggle")},n=p=>{q("action",p.key,v.item)};return(p,B)=>(r(),d("div",Ye,[l(T,{variant:"view",size:"sm",shape:"round","icon-only":!0,tooltip:"Actions",onClick:R},{icon:i(()=>[l(h(Ke),{class:"h-4 w-4"})]),_:1}),l(Ee,{"enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:i(()=>[p.isOpen?(r(),d("div",{key:0,class:"absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5",onClick:B[0]||(B[0]=je(()=>{},["stop"]))},[t("div",Me,[(r(!0),d(Q,null,X(m.value,k=>(r(),d("button",{key:k.key,onClick:V=>n(k),class:A(["flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",k.class])},[(r(),I(Z(k.icon),{class:"h-4 w-4 mr-3"})),c(" "+o(k.label),1)],10,Je))),128))])])):_("",!0)]),_:1})]))}}),C={view:{key:"view",label:"View Details",icon:Oe},edit:{key:"edit",label:"Edit",icon:z},approve:{key:"approve",label:"Approve",icon:ee,class:"text-green-600 hover:bg-green-50"},reject:{key:"reject",label:"Reject",icon:K,class:"text-red-600 hover:bg-red-50"}},We={class:"space-y-6"},Ge={class:"flex items-center justify-between"},Qe={class:"flex items-center space-x-4"},Xe={class:"text-2xl font-bold text-gray-900"},Ze={class:"mt-1 text-sm text-gray-500"},et={class:"flex items-center space-x-3"},tt={key:0,class:"flex justify-center items-center py-12"},st={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4"},at={class:"flex"},nt={class:"flex-shrink-0"},ot={class:"ml-3"},it={class:"mt-2 text-sm text-red-700"},lt={key:2,class:"bg-white rounded-xl shadow-sm border border-gray-200"},rt={class:"px-6 py-4 border-b border-gray-200"},ct={class:"flex items-center justify-between"},dt={class:"p-6"},ut={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},mt={class:"space-y-4"},pt={class:"space-y-3"},gt={class:"mt-1 text-sm text-gray-900"},vt={class:"mt-1 text-sm text-gray-900 font-mono"},yt={class:"mt-1 text-sm text-gray-900"},ft={class:"mt-1 text-sm text-gray-900"},_t={class:"space-y-4"},xt={class:"space-y-3"},bt={class:"mt-1 text-sm text-gray-900"},ht=["href"],wt={class:"mt-1 text-sm text-gray-900"},kt=["href"],Ct={class:"mt-1 text-sm text-gray-900"},At={class:"space-y-4"},qt={class:"space-y-3"},Rt={class:"mt-1"},Lt={class:"mt-1 text-sm text-gray-900 font-medium"},Nt={class:"mt-1 text-sm text-gray-900"},$t={class:"space-y-4"},Dt={class:"space-y-3"},Tt={class:"mt-1 text-sm text-gray-900 font-mono"},Bt={class:"mt-1 text-sm text-gray-900 font-mono"},Et={class:"mt-1 text-sm text-gray-900 font-mono"},jt={class:"mt-1 text-sm text-gray-900"},St={class:"space-y-4"},Ft={class:"space-y-3"},It={class:"mt-1 text-sm text-gray-900"},Pt={key:0},Vt={class:"mt-1 text-sm text-gray-900"},zt={key:1},Kt={class:"mt-1 text-sm text-gray-900"},Ot={key:3,class:"bg-white rounded-xl shadow-sm border border-gray-200"},Ht={class:"border-b border-gray-200"},Ut={class:"-mb-px flex space-x-8 px-6","aria-label":"Tabs"},Yt=["onClick"],Mt={key:0,class:"ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium"},Jt={class:"p-6"},Wt={key:0,class:"space-y-4"},Gt={class:"flex items-center justify-between"},Qt={key:1,class:"space-y-4"},Xt={key:2,class:"space-y-4"},Zt={class:"flex items-center justify-between"},es={key:3,class:"space-y-4"},ts={key:4,class:"space-y-4"},ss={class:"flex items-center justify-between"},as={class:"text-center py-12 text-gray-500"},fs=G({__name:"ClientsView",setup(O){const P=Fe(),v=Ie(),q=b(!0),m=b(!1),R=b(""),n=b(null),p=b("loan-accounts"),B=b([]),k=b([]),V=b([]),H=b([]),$=b({}),U=[{id:"loan-accounts",name:"Loan Accounts",icon:Pe,count:0},{id:"loan-requests",name:"Requests",icon:Ve,count:0},{id:"limits",name:"Limits",icon:ze},{id:"repayments",name:"Repayments",icon:Ue,count:0},{id:"kycs",name:"KYCs",icon:J,count:0}],te={loan_number:"Account No",customer_name:"Customer",dob:"DOB",actual_balance:"Wallet Balance",loan_balance:"Loan Balance",kyc_confirm:"KYC Status",status:"Account Status",created:"Created"},se={req_number:"Request No",name:"Customer",product_name:"Product",requested_amount:"Requested",approved_amount:"Approved",interest_charged:"To Repay",approval_status:"Status",created:"Requested On",completed_on:"Approved On",repayment_date:"Repayment Date"},ae={reference_id:"Reference",current_limit:"Current Limit",requested_limit:"Requested Limit",status:"Status",created:"Date"},ne={trxn_code:"Transaction Code",payer:"Customer",repayment_amount:"Amount Paid",reducing_balance:"Balance",repayment_source:"Source",created:"Date"},oe=[C.view,C.edit],ie=[C.view,{...C.approve,condition:s=>s.approval_status==="5"},{...C.reject,condition:s=>s.approval_status==="5"}],le=[C.view,C.edit],re=[C.view],ce=async()=>{const s=P.params.id;if(!s){R.value="Client ID is required",q.value=!1;return}try{q.value=!0;const y=JSON.parse(localStorage.getItem("clients")||"[]").find(g=>g.client_id===s);if(y)n.value=y,await E();else{const g=await w.getClientForEdit(s);g.status===200?(n.value=g.message,await E()):R.value=g.message||"Failed to load client details"}}catch(e){R.value=e.message||"An error occurred while loading client details"}finally{q.value=!1}},E=async()=>{if(n.value)try{const[s,e,y,g]=await Promise.all([w.getClientLoanAccounts({client_id:n.value.client_id,limit:1}),w.getClientLoanRequests({client_id:n.value.client_id,limit:1}),w.getClientLoanRepayments({client_id:n.value.client_id,limit:1}),w.getClientLimitRequests({client_id:n.value.client_id,limit:1})]);U.forEach(f=>{var N,a,u,D;switch(f.id){case"loan-accounts":f.count=s.status===200&&((N=s.message)==null?void 0:N.total_count)||0;break;case"loan-requests":f.count=e.status===200&&((a=e.message)==null?void 0:a.total_count)||0;break;case"repayments":f.count=y.status===200&&((u=y.message)==null?void 0:u.total_count)||0;break;case"limits":f.count=g.status===200&&((D=g.message)==null?void 0:D.total_count)||0;break;case"kycs":f.count=0;break}})}catch(s){console.error("Error fetching tab data:",s)}},de=()=>{n.value&&v.push({name:"clients-edit",params:{id:n.value.client_id}})},ue=s=>{let e=0,y=parseFloat(s.approved_amount),g=parseFloat(s.interest_charged)*100,f=parseFloat(s.repayment_period_in_months),N=parseFloat(s.procesing_fee),a=parseFloat(s.excise_duty);return e=y+y*(g/100)*f+N+a,e},me=async()=>{n.value&&console.log("Toggle status for client:",n.value.client_name)},pe=async()=>{var s;if(n.value){m.value=!0;try{const e=await w.getClientLoanAccounts({client_id:n.value.client_id,limit:10,offset:1});e.status===200&&(B.value=((s=e.message)==null?void 0:s.data)||[])}catch(e){console.error("Error fetching loan accounts:",e)}finally{m.value=!1}}},ge=async()=>{var s;if(n.value){m.value=!0;try{const e=await w.getClientLoanRequests({client_id:n.value.client_id,limit:10,offset:1});e.status===200&&(k.value=((s=e.message)==null?void 0:s.data)||[])}catch(e){console.error("Error fetching loan requests:",e)}finally{m.value=!1}}},ve=async()=>{var s;if(n.value){m.value=!0;try{const e=await w.getClientLoanRepayments({client_id:n.value.client_id,limit:10,offset:1});e.status===200&&(V.value=((s=e.message)==null?void 0:s.data)||[])}catch(e){console.error("Error fetching repayments:",e)}finally{m.value=!1}}},ye=async()=>{var s;if(n.value){m.value=!0;try{const e=await w.getClientLimitRequests({client_id:n.value.client_id,limit:10,offset:1});e.status===200&&(H.value=((s=e.message)==null?void 0:s.data)||[])}catch(e){console.error("Error fetching limits:",e)}finally{m.value=!1}}},Y=async s=>{switch(p.value=s,s){case"loan-accounts":await pe();break;case"loan-requests":await ge();break;case"repayments":await ve();break;case"limits":await ye();break}},fe=s=>{switch(parseInt(s)){case 1:return"bg-green-100 text-green-800";case 2:return"bg-orange-100 text-orange-800";case 3:return"bg-red-100 text-red-800";case 4:return"bg-purple-100 text-purple-800";case 5:return"bg-blue-100 text-blue-800";case 6:return"bg-yellow-100 text-yellow-800";case 7:return"bg-red-100 text-red-800";case 8:return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},_e=s=>{switch(parseInt(s)){case 1:return"Fully Paid";case 2:return"Partially Paid";case 3:return"Rejected";case 4:return"Unverified";case 5:return"Pending";case 6:return"Unpaid";case 7:return"Failure";case 8:return"Approved";default:return"Unknown"}},xe=s=>{switch(parseInt(s)){case 1:return"bg-green-100 text-green-800";case 2:return"bg-orange-100 text-orange-800";case 3:return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},be=s=>{switch(parseInt(s)){case 1:return"Approved";case 2:return"Pending";case 3:return"Rejected";default:return"Unknown"}},x=s=>{const e=typeof s=="string"?parseFloat(s):s;return new Intl.NumberFormat("en-KE",{style:"currency",currency:"KES"}).format(e||0)},L=s=>s?new Date(s).toLocaleDateString("en-KE",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"N/A",M=s=>{const e=parseInt(s);if(e>=11&&e<=13)return"th";switch(e%10){case 1:return"st";case 2:return"nd";case 3:return"rd";default:return"th"}},j=s=>{$.value={[s]:!$.value[s]}},he=(s,e)=>{switch(s){case"view":Ae(e);break;case"edit":qe(e);break}},we=(s,e)=>{switch(s){case"view":Re(e);break;case"approve":Le(e);break;case"reject":Ne(e);break}},ke=(s,e)=>{switch(s){case"view":$e(e);break;case"edit":De(e);break}},Ce=(s,e)=>{switch(s){case"view":Te(e);break}},Ae=s=>{var e;console.log("View loan account:",s),v.push({name:"loan-accounts",query:{client_id:(e=n.value)==null?void 0:e.client_id,account:s.loan_number}})},qe=s=>{var e;console.log("Edit loan account:",s),v.push({name:"loan-accounts",query:{client_id:(e=n.value)==null?void 0:e.client_id,edit:s.loan_number}})},Re=s=>{var e;console.log("View loan request:",s),v.push({name:"requests",query:{client_id:(e=n.value)==null?void 0:e.client_id,request:s.req_number}})},Le=async s=>{console.log("Approve loan request:",s);try{await E()}catch(e){console.error("Failed to approve loan request:",e)}},Ne=async s=>{console.log("Reject loan request:",s);try{await E()}catch(e){console.error("Failed to reject loan request:",e)}},$e=s=>{var e;console.log("View limit:",s),v.push({name:"limits",query:{client_id:(e=n.value)==null?void 0:e.client_id,limit:s.reference_id}})},De=s=>{var e;console.log("Edit limit:",s),v.push({name:"limits",query:{client_id:(e=n.value)==null?void 0:e.client_id,edit:s.reference_id}})},Te=s=>{var e;console.log("View repayment:",s),v.push({name:"loan-repayments",query:{client_id:(e=n.value)==null?void 0:e.client_id,transaction:s.trxn_code}})};return Se(async()=>{await ce(),n.value&&await Y("loan-accounts")}),(s,e)=>{var y,g,f,N;return r(),d("div",We,[t("div",Ge,[t("div",Qe,[t("button",{onClick:e[0]||(e[0]=a=>s.$router.go(-1)),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[l(h(He),{class:"h-4 w-4 mr-2"}),e[1]||(e[1]=c(" Back "))]),t("div",null,[t("h1",Xe,o(((y=n.value)==null?void 0:y.client_name)||"Client Details"),1),t("p",Ze," Account: "+o(((g=n.value)==null?void 0:g.client_account)||"Loading..."),1)])]),t("div",et,[l(T,{variant:"edit",size:"md",shape:"rounded",tooltip:"Edit Client",onClick:de},{icon:i(()=>[l(h(z),{class:"h-4 w-4"})]),default:i(()=>[e[2]||(e[2]=c(" Edit "))]),_:1,__:[2]}),l(T,{variant:((f=n.value)==null?void 0:f.client_status)==="1"?"danger":"success",size:"md",shape:"rounded",tooltip:((N=n.value)==null?void 0:N.client_status)==="1"?"Deactivate Client":"Activate Client",onClick:me},{icon:i(()=>{var a;return[((a=n.value)==null?void 0:a.client_status)==="1"?(r(),I(h(K),{key:0,class:"h-4 w-4"})):(r(),I(h(ee),{key:1,class:"h-4 w-4"}))]}),default:i(()=>{var a;return[c(" "+o(((a=n.value)==null?void 0:a.client_status)==="1"?"Deactivate":"Activate"),1)]}),_:1},8,["variant","tooltip"])])]),q.value?(r(),d("div",tt,e[3]||(e[3]=[t("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):R.value?(r(),d("div",st,[t("div",at,[t("div",nt,[l(h(K),{class:"h-5 w-5 text-red-400"})]),t("div",ot,[e[4]||(e[4]=t("h3",{class:"text-sm font-medium text-red-800"},"Error loading client details",-1)),t("div",it,[t("p",null,o(R.value),1)])])])])):n.value?(r(),d("div",lt,[t("div",rt,[t("div",ct,[e[5]||(e[5]=t("h3",{class:"text-lg font-medium text-gray-900"},"Client Information",-1)),t("span",{class:A(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",{"bg-green-100 text-green-800":n.value.client_status==="1","bg-red-100 text-red-800":n.value.client_status==="0"}])},o(n.value.client_status==="1"?"Active":"Inactive"),3)])]),t("div",dt,[t("div",ut,[t("div",mt,[e[10]||(e[10]=t("h4",{class:"text-sm font-medium text-gray-900 uppercase tracking-wide"},"Basic Information",-1)),t("div",pt,[t("div",null,[e[6]||(e[6]=t("dt",{class:"text-sm font-medium text-gray-500"},"Client Name",-1)),t("dd",gt,o(n.value.client_name),1)]),t("div",null,[e[7]||(e[7]=t("dt",{class:"text-sm font-medium text-gray-500"},"Account Number",-1)),t("dd",vt,o(n.value.client_account),1)]),t("div",null,[e[8]||(e[8]=t("dt",{class:"text-sm font-medium text-gray-500"},"Currency",-1)),t("dd",yt,o(n.value.currency_code),1)]),t("div",null,[e[9]||(e[9]=t("dt",{class:"text-sm font-medium text-gray-500"},"Service Fee",-1)),t("dd",ft,o((parseFloat(n.value.service_fee)*100).toFixed(3))+"%",1)])])]),t("div",_t,[e[14]||(e[14]=t("h4",{class:"text-sm font-medium text-gray-900 uppercase tracking-wide"},"Contact Information",-1)),t("div",xt,[t("div",null,[e[11]||(e[11]=t("dt",{class:"text-sm font-medium text-gray-500"},"Email Address",-1)),t("dd",bt,[t("a",{href:`mailto:${n.value.client_email}`,class:"text-blue-600 hover:text-blue-500"},o(n.value.client_email),9,ht)])]),t("div",null,[e[12]||(e[12]=t("dt",{class:"text-sm font-medium text-gray-500"},"Phone Number",-1)),t("dd",wt,[t("a",{href:`tel:${n.value.client_phone}`,class:"text-blue-600 hover:text-blue-500"},o(n.value.client_phone),9,kt)])]),t("div",null,[e[13]||(e[13]=t("dt",{class:"text-sm font-medium text-gray-500"},"Address",-1)),t("dd",Ct,o(n.value.client_address||"Not provided"),1)])])]),t("div",At,[e[18]||(e[18]=t("h4",{class:"text-sm font-medium text-gray-900 uppercase tracking-wide"},"Business Information",-1)),t("div",qt,[t("div",null,[e[15]||(e[15]=t("dt",{class:"text-sm font-medium text-gray-500"},"Can Issue Loans",-1)),t("dd",Rt,[t("span",{class:A(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",{"bg-green-100 text-green-800":n.value.can_issue_loans==="1","bg-red-100 text-red-800":n.value.can_issue_loans==="0"}])},o(n.value.can_issue_loans==="1"?"Yes":"No"),3)])]),t("div",null,[e[16]||(e[16]=t("dt",{class:"text-sm font-medium text-gray-500"},"Total Loan Assets",-1)),t("dd",Lt,o(x(n.value.total_loan_assets)),1)]),t("div",null,[e[17]||(e[17]=t("dt",{class:"text-sm font-medium text-gray-500"},"Operating Hours",-1)),t("dd",Nt,o(n.value.open_date)+o(M(n.value.open_date))+" - "+o(n.value.close_date)+o(M(n.value.close_date))+" of month ",1)])])]),t("div",$t,[e[23]||(e[23]=t("h4",{class:"text-sm font-medium text-gray-900 uppercase tracking-wide"},"Payment Configuration",-1)),t("div",Dt,[t("div",null,[e[19]||(e[19]=t("dt",{class:"text-sm font-medium text-gray-500"},"B2C PayBill",-1)),t("dd",Tt,o(n.value.b2c_paybill||"Not configured"),1)]),t("div",null,[e[20]||(e[20]=t("dt",{class:"text-sm font-medium text-gray-500"},"C2B PayBill",-1)),t("dd",Bt,o(n.value.c2b_paybill||"Not configured"),1)]),t("div",null,[e[21]||(e[21]=t("dt",{class:"text-sm font-medium text-gray-500"},"B2B PayBill",-1)),t("dd",Et,o(n.value.b2b_paybill||"Not configured"),1)]),t("div",null,[e[22]||(e[22]=t("dt",{class:"text-sm font-medium text-gray-500"},"SMS Sender ID",-1)),t("dd",jt,o(n.value.sender_id||"Not configured"),1)])])]),t("div",St,[e[27]||(e[27]=t("h4",{class:"text-sm font-medium text-gray-900 uppercase tracking-wide"},"Important Dates",-1)),t("div",Ft,[t("div",null,[e[24]||(e[24]=t("dt",{class:"text-sm font-medium text-gray-500"},"Created",-1)),t("dd",It,o(L(n.value.created)),1)]),n.value.activated_on?(r(),d("div",Pt,[e[25]||(e[25]=t("dt",{class:"text-sm font-medium text-gray-500"},"Activated",-1)),t("dd",Vt,o(L(n.value.activated_on)),1)])):_("",!0),n.value.deactivated_on?(r(),d("div",zt,[e[26]||(e[26]=t("dt",{class:"text-sm font-medium text-gray-500"},"Deactivated",-1)),t("dd",Kt,o(L(n.value.deactivated_on)),1)])):_("",!0)])])])])])):_("",!0),n.value?(r(),d("div",Ot,[t("div",Ht,[t("nav",Ut,[(r(),d(Q,null,X(U,a=>t("button",{key:a.id,onClick:u=>Y(a.id),class:A([p.value===a.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300","whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center"])},[(r(),I(Z(a.icon),{class:"h-5 w-5 mr-2"})),c(" "+o(a.name)+" ",1),a.count!==void 0?(r(),d("span",Mt,o(a.count),1)):_("",!0)],10,Yt)),64))])]),t("div",Jt,[p.value==="loan-accounts"?(r(),d("div",Wt,[t("div",Gt,[e[28]||(e[28]=t("h3",{class:"text-lg font-medium text-gray-900"},"Loan Accounts",-1)),l(T,{variant:"add",size:"sm",shape:"round","icon-only":!0,tooltip:"Add Account"},{icon:i(()=>[l(h(W),{class:"h-4 w-4"})]),_:1})]),l(S,{data:B.value,headers:te,loading:m.value,title:"Loan Accounts","row-key":"loan_number","has-actions":!0,"empty-message":"No loan accounts found for this client."},{"cell-customer_name":i(({item:a})=>[c(o(a.first_name)+" "+o(a.last_name),1)]),"cell-actual_balance":i(({value:a})=>[c(o(x(a)),1)]),"cell-loan_balance":i(({value:a})=>[c(o(x(a)),1)]),"cell-kyc_confirm":i(({value:a})=>[t("span",{class:A([a==="1"?"bg-green-100 text-green-800":"bg-red-100 text-red-800","inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(a==="1"?"Completed":"Not Completed"),3)]),"cell-status":i(({value:a})=>[t("span",{class:A([a==="1000"?"bg-green-100 text-green-800":"bg-red-100 text-red-800","inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(a==="1000"?"Active":"Inactive"),3)]),"cell-created":i(({value:a})=>[c(o(L(a)),1)]),actions:i(({item:a,index:u})=>[l(F,{item:a,actions:oe,"is-open":$.value[u],onToggle:D=>j(u),onAction:he},null,8,["item","is-open","onToggle"])]),_:1},8,["data","loading"])])):_("",!0),p.value==="loan-requests"?(r(),d("div",Qt,[e[29]||(e[29]=t("div",{class:"flex items-center justify-between"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Loan Requests")],-1)),l(S,{data:k.value,headers:se,loading:m.value,title:"Loan Requests","row-key":"req_number","has-actions":!0,"empty-message":"No loan requests found for this client."},{"cell-name":i(({value:a})=>[c(o(a),1)]),"cell-requested_amount":i(({value:a})=>[c(o(x(a)),1)]),"cell-approved_amount":i(({value:a})=>[c(o(x(a)),1)]),"cell-approval_status":i(({value:a})=>[t("span",{class:A([fe(a),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(_e(a)),3)]),"cell-interest_charged":i(({item:a})=>[c(o(x(ue(a))),1)]),"cell-created":i(({value:a})=>[c(o(L(a)),1)]),actions:i(({item:a,index:u})=>[l(F,{item:a,actions:ie,"is-open":$.value[u],onToggle:D=>j(u),onAction:we},null,8,["item","is-open","onToggle"])]),_:1},8,["data","loading"])])):_("",!0),p.value==="limits"?(r(),d("div",Xt,[t("div",Zt,[e[30]||(e[30]=t("h3",{class:"text-lg font-medium text-gray-900"},"Client Limits",-1)),l(T,{variant:"edit",size:"sm",shape:"round","icon-only":!0,tooltip:"Edit Limits"},{icon:i(()=>[l(h(z),{class:"h-4 w-4"})]),_:1})]),l(S,{data:H.value,headers:ae,loading:m.value,title:"Client Limits","row-key":"reference_id","has-actions":!0,"empty-message":"No limits configured for this client."},{"cell-current_limit":i(({value:a})=>[c(o(x(a)),1)]),"cell-requested_limit":i(({value:a})=>[c(o(x(a)),1)]),"cell-status":i(({value:a})=>[t("span",{class:A([xe(a),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(be(a)),3)]),"cell-created":i(({value:a})=>[c(o(L(a)),1)]),actions:i(({item:a,index:u})=>[l(F,{item:a,actions:le,"is-open":$.value[u],onToggle:D=>j(u),onAction:ke},null,8,["item","is-open","onToggle"])]),_:1},8,["data","loading"])])):_("",!0),p.value==="repayments"?(r(),d("div",es,[e[31]||(e[31]=t("div",{class:"flex items-center justify-between"},[t("h3",{class:"text-lg font-medium text-gray-900"},"Repayments")],-1)),l(S,{data:V.value,headers:ne,loading:m.value,title:"Repayments","row-key":"trxn_code","has-actions":!0,"empty-message":"No repayments found for this client."},{"cell-repayment_amount":i(({value:a})=>[c(o(x(a)),1)]),"cell-reducing_balance":i(({value:a})=>[c(o(x(a)),1)]),"cell-created":i(({value:a})=>[c(o(L(a)),1)]),actions:i(({item:a,index:u})=>[l(F,{item:a,actions:re,"is-open":$.value[u],onToggle:D=>j(u),onAction:Ce},null,8,["item","is-open","onToggle"])]),_:1},8,["data","loading"])])):_("",!0),p.value==="kycs"?(r(),d("div",ts,[t("div",ss,[e[33]||(e[33]=t("h3",{class:"text-lg font-medium text-gray-900"},"KYC Documents",-1)),l(T,{variant:"add",size:"sm",shape:"rounded"},{icon:i(()=>[l(h(W),{class:"h-4 w-4"})]),default:i(()=>[e[32]||(e[32]=c(" Upload Document "))]),_:1,__:[32]})]),t("div",as,[l(h(J),{class:"h-12 w-12 mx-auto mb-4 text-gray-300"}),e[34]||(e[34]=t("p",null,"No KYC documents found for this client.",-1))])])):_("",!0)])])):_("",!0)])}}});export{fs as default};
