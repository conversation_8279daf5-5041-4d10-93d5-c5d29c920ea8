import{d as N,r as a,a as q,p as E,c as p,b as e,j as F,i as L,t as r,h as b,C as y,F as W,m as Q,e as D,k as i,q as G,f as J,z as K,n as O,o as c}from"./index-DOaBqVmr.js";import{_ as X}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";const Y={class:"space-y-6"},tt={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},et={class:"flex items-center justify-between"},ot={class:"flex space-x-3"},st=["disabled"],lt={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},nt={key:1,class:"-ml-1 mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},at={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},rt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},it=["value"],dt={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"},ut={class:"text-gray-900 font-medium"},pt={class:"text-gray-900 font-medium"},ct={class:"text-gray-900 font-medium"},mt={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"},_t={class:"flex items-center space-x-2"},ft=["onClick"],gt=["onClick"],bt=["onClick"],wt=N({__name:"PartnersBetSlips",setup(yt){O();const w=G(),d=a(!1),h=a([]),k=a([]),m=a(1),C=a(0),x=a(10),v=a(""),f=a(""),S=a("asc"),n=q({partner_id:"",slip_status:"",slip_type:"",date_range:""}),U=[{slip_id:"SLIP001",partner_name:"SportsBet Pro",partner_id:1,customer_id:"CUST001",slip_type:"multiple",slip_status:"pending",bet_count:3,total_stake:150,potential_payout:875,actual_payout:null,total_odds:"5.83",created_at:"2024-01-31T14:30:00Z",settled_at:null},{slip_id:"SLIP002",partner_name:"SportsBet Pro",partner_id:1,customer_id:"CUST002",slip_type:"single",slip_status:"settled",bet_count:1,total_stake:50,potential_payout:92.5,actual_payout:92.5,total_odds:"1.85",created_at:"2024-01-31T12:15:00Z",settled_at:"2024-01-31T16:45:00Z"},{slip_id:"SLIP003",partner_name:"BetMaster",partner_id:2,customer_id:"CUST003",slip_type:"accumulator",slip_status:"settled",bet_count:5,total_stake:25,potential_payout:450,actual_payout:0,total_odds:"18.00",created_at:"2024-01-31T10:00:00Z",settled_at:"2024-01-31T13:30:00Z"},{slip_id:"SLIP004",partner_name:"WinBig Casino",partner_id:3,customer_id:"CUST004",slip_type:"system",slip_status:"partially_settled",bet_count:4,total_stake:100,potential_payout:320,actual_payout:80,total_odds:"3.20",created_at:"2024-01-31T09:45:00Z",settled_at:"2024-01-31T15:20:00Z"}],M=[{id:1,name:"SportsBet Pro"},{id:2,name:"BetMaster"},{id:3,name:"WinBig Casino"}],u=async()=>{d.value=!0;try{await new Promise(l=>setTimeout(l,1e3));let s=[...U];if(w.query.bet_id&&(s=s.filter(l=>l.slip_id.includes(w.query.bet_id))),n.partner_id&&(s=s.filter(l=>l.partner_id===parseInt(n.partner_id))),n.slip_status&&(s=s.filter(l=>l.slip_status===n.slip_status)),n.slip_type&&(s=s.filter(l=>l.slip_type===n.slip_type)),v.value){const l=v.value.toLowerCase();s=s.filter(_=>_.slip_id.toLowerCase().includes(l)||_.partner_name.toLowerCase().includes(l)||_.customer_id.toLowerCase().includes(l))}f.value&&s.sort((l,_)=>{const B=l[f.value],T=_[f.value],V=S.value==="asc"?1:-1;return B<T?-1*V:B>T?1*V:0}),C.value=s.length;const o=(m.value-1)*x.value,t=o+x.value;h.value=s.slice(o,t),k.value=M}catch(s){console.error("Error loading partners bet slips:",s)}finally{d.value=!1}},A=()=>{u()},R=s=>{m.value=s,u()},$=s=>{v.value=s,m.value=1,u()},Z=(s,o)=>{f.value=s,S.value=o,u()},z=s=>{P(s)},g=()=>{m.value=1,u()},P=s=>{console.log("View bet slip:",s)},j=s=>{console.log("Download bet slip:",s)},H=s=>{console.log("Cancel bet slip:",s)},I=()=>{console.log("Export partners bet slips data")};return E(()=>{u()}),(s,o)=>(c(),p("div",Y,[e("div",tt,[e("div",et,[o[7]||(o[7]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Partners Bet Slips"),e("p",{class:"mt-1 text-sm text-gray-600"}," View and manage detailed bet slip information from all partners ")],-1)),e("div",ot,[e("button",{onClick:A,disabled:d.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"},[d.value?(c(),p("svg",lt,o[4]||(o[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):(c(),p("svg",nt,o[5]||(o[5]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))),L(" "+r(d.value?"Refreshing...":"Refresh"),1)],8,st),e("button",{onClick:I,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},o[6]||(o[6]=[e("svg",{class:"-ml-1 mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),L(" Export ")]))])])]),e("div",at,[e("div",rt,[e("div",null,[o[9]||(o[9]=e("label",{for:"partner-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Partner",-1)),b(e("select",{id:"partner-filter","onUpdate:modelValue":o[0]||(o[0]=t=>n.partner_id=t),onChange:g,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},[o[8]||(o[8]=e("option",{value:""},"All Partners",-1)),(c(!0),p(W,null,Q(k.value,t=>(c(),p("option",{key:t.id,value:t.id},r(t.name),9,it))),128))],544),[[y,n.partner_id]])]),e("div",null,[o[11]||(o[11]=e("label",{for:"slip-status-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Slip Status",-1)),b(e("select",{id:"slip-status-filter","onUpdate:modelValue":o[1]||(o[1]=t=>n.slip_status=t),onChange:g,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},o[10]||(o[10]=[D('<option value="">All Statuses</option><option value="pending">Pending</option><option value="settled">Settled</option><option value="cancelled">Cancelled</option><option value="partially_settled">Partially Settled</option>',5)]),544),[[y,n.slip_status]])]),e("div",null,[o[13]||(o[13]=e("label",{for:"slip-type-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Slip Type",-1)),b(e("select",{id:"slip-type-filter","onUpdate:modelValue":o[2]||(o[2]=t=>n.slip_type=t),onChange:g,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},o[12]||(o[12]=[D('<option value="">All Types</option><option value="single">Single</option><option value="multiple">Multiple</option><option value="system">System</option><option value="accumulator">Accumulator</option>',5)]),544),[[y,n.slip_type]])]),e("div",null,[o[15]||(o[15]=e("label",{for:"date-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Date Range",-1)),b(e("select",{id:"date-filter","onUpdate:modelValue":o[3]||(o[3]=t=>n.date_range=t),onChange:g,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},o[14]||(o[14]=[e("option",{value:""},"All Time",-1),e("option",{value:"today"},"Today",-1),e("option",{value:"week"},"This Week",-1),e("option",{value:"month"},"This Month",-1)]),544),[[y,n.date_range]])])])]),F(X,{data:h.value,loading:d.value,"current-page":m.value,"total-records":C.value,"page-size":x.value,title:"Partners Bet Slips","row-key":"slip_id","has-actions":!0,onPageChange:R,onSearch:$,onSort:Z,onRowClick:z},{"cell-slip_status":i(({value:t})=>[e("span",{class:K([{"bg-yellow-100 text-yellow-800":t==="pending","bg-green-100 text-green-800":t==="settled","bg-red-100 text-red-800":t==="cancelled","bg-blue-100 text-blue-800":t==="partially_settled"},"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},r(t?t.replace("_"," ").replace(/\b\w/g,l=>l.toUpperCase()):"-"),3)]),"cell-slip_type":i(({value:t})=>[e("span",dt,r(t?t.charAt(0).toUpperCase()+t.slice(1):"-"),1)]),"cell-total_stake":i(({value:t})=>[e("span",ut," $"+r(t?t.toLocaleString():"0"),1)]),"cell-potential_payout":i(({value:t})=>[e("span",pt," $"+r(t?t.toLocaleString():"0"),1)]),"cell-actual_payout":i(({value:t})=>[e("span",ct,r(t!==null?`$${t.toLocaleString()}`:"-"),1)]),"cell-bet_count":i(({value:t})=>[e("span",mt,r(t)+" "+r(t===1?"bet":"bets"),1)]),actions:i(({item:t})=>[e("div",_t,[e("button",{onClick:l=>P(t),class:"text-blue-600 hover:text-blue-900 text-sm font-medium transition-colors duration-200"}," View ",8,ft),e("button",{onClick:l=>j(t),class:"text-indigo-600 hover:text-indigo-900 text-sm font-medium transition-colors duration-200"}," Download ",8,gt),t.slip_status==="pending"?(c(),p("button",{key:0,onClick:l=>H(t),class:"text-red-600 hover:text-red-900 text-sm font-medium transition-colors duration-200"}," Cancel ",8,bt)):J("",!0)])]),_:1},8,["data","loading","current-page","total-records","page-size"])]))}});export{wt as default};
