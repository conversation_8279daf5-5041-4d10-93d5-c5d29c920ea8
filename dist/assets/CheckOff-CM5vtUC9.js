import{d as W,u as G,r as n,a as J,p as X,c as p,b as s,j as m,i as D,y,k as c,f as v,t as l,z as Y,n as Z,o as _}from"./index-DOaBqVmr.js";import{_ as ee}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{_ as te}from"./FilterCards.vue_vue_type_script_setup_true_lang-Bk3N6i80.js";import{l as oe}from"./loanApi-CajqAy4f.js";import{r as se}from"./ArrowPathIcon-BCh5HUKO.js";import{r as ae}from"./EllipsisVerticalIcon-BCCOPEML.js";import{r as ne}from"./ArrowDownTrayIcon-BWjpub36.js";const le={class:"space-y-6"},re={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},ce={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},ie={class:"mt-4 sm:mt-0 flex space-x-3"},ue={class:"flex space-x-2"},de={class:"text-sm"},fe={class:"font-medium text-gray-900"},me={class:"text-gray-500"},ge={class:"text-xs text-gray-400"},be={class:"text-sm font-medium text-gray-900"},pe={class:"text-sm font-medium text-gray-900"},_e={class:"text-sm font-medium text-gray-900"},ye={class:"relative"},ve=["onClick"],he={key:0,class:"absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},xe={class:"py-1"},ke=["onClick"],Ce=["onClick"],we=["onClick"],Ae=["onClick"],Se=["onClick"],ze=W({__name:"CheckOff",setup(De){const h=Z(),x=G(),r=n(!1),g=n([]),i=n(1),b=n(0),k=n(10),C=n(""),$=n(""),E=n("asc"),u=J({}),d=n({client_id:x.selectedClientId||"",status:"",type_id:"",start_date:"",end_date:""}),F=[{value:"1000",label:"Active"},{value:"1002",label:"New"},{value:"1003",label:"Unverified"},{value:"1004",label:"Suspended"},{value:"1005",label:"Dormant"}],R=[{key:"customer",label:"Customer",sortable:!1},{key:"loan_number",label:"Loan Account",sortable:!0},{key:"max_approved_loan_amount",label:"Max Limit",sortable:!0},{key:"actual_balance",label:"Wallet Balance",sortable:!0},{key:"status",label:"Status",sortable:!0}],a=async()=>{var e,o;r.value=!0;try{const t=await oe.getCheckOffReports({page:i.value,limit:k.value,search:C.value,...d.value});t.status===200?(g.value=((e=t.message)==null?void 0:e.data)||[],b.value=((o=t.message)==null?void 0:o.total_count)||0):(g.value=[],b.value=0)}catch(t){console.error("Error fetching check-off reports:",t),g.value=[],b.value=0}finally{r.value=!1}},O=e=>{i.value=e,a()},N=e=>{C.value=e,i.value=1,a()},V=(e,o)=>{$.value=e,E.value=o,a()},z=e=>{w(e)},B=e=>{Object.assign(d.value,e)},P=()=>{a()},U=e=>{d.value={...e},i.value=1,a()},I=()=>{d.value={client_id:x.selectedClientId||"",status:"",type_id:"",start_date:"",end_date:""},i.value=1,a()},M=e=>{Object.keys(u).forEach(o=>{parseInt(o)!==e&&(u[parseInt(o)]=!1)}),u[e]=!u[e]},w=e=>{console.log("View details for account:",e.loan_number)},j=e=>{h.push({name:"loan-accounts-edit",params:{id:e.loan_number}})},T=e=>{h.push({name:"transactions",query:{loan_number:e.loan_number}})},L=async e=>{try{const o=prompt("Please enter reason for blocking this account:");if(!o||!confirm(`Are you sure you want to block account ${e.loan_number}?`))return;r.value=!0,console.log("Block account:",e.loan_number,"Reason:",o),alert("Account blocked successfully"),a()}catch(o){console.error("Error blocking account:",o),alert("Failed to block account. Please try again.")}finally{r.value=!1}},K=async e=>{try{if(!confirm(`Are you sure you want to unblock account ${e.loan_number}?`))return;r.value=!0,console.log("Unblock account:",e.loan_number),alert("Account unblocked successfully"),a()}catch(o){console.error("Error unblocking account:",o),alert("Failed to unblock account. Please try again.")}finally{r.value=!1}},q=()=>{console.log("Export check-off reports data")},A=e=>{const o=typeof e=="string"?parseFloat(e):e;return new Intl.NumberFormat("en-KE",{style:"currency",currency:"KES",minimumFractionDigits:0,maximumFractionDigits:0}).format(o)},H=e=>({1e3:"Active",1002:"New",1003:"Unverified",1004:"Suspended",1005:"Dormant"})[e]||"Unknown",Q=e=>({1e3:"bg-green-100 text-green-800",1002:"bg-blue-100 text-blue-800",1003:"bg-yellow-100 text-yellow-800",1004:"bg-red-100 text-red-800",1005:"bg-gray-100 text-gray-800"})[e]||"bg-gray-100 text-gray-800";return X(()=>{a()}),(e,o)=>(_(),p("div",le,[s("div",re,[s("div",ce,[o[1]||(o[1]=s("div",null,[s("h1",{class:"text-2xl font-bold text-gray-900"},"Loan Check-Off"),s("p",{class:"text-gray-600 mt-1"},"Manage salary deduction for loan repayments")],-1)),s("div",ie,[s("button",{onClick:P,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[m(y(se),{class:"w-4 h-4 mr-2"}),o[0]||(o[0]=D(" Refresh "))])])])]),m(te,{filters:d.value,"filter-type":"checkoff","status-options":F,"onUpdate:filters":B,onApply:U,onClear:I},null,8,["filters"]),m(ee,{data:g.value,headers:R,loading:r.value,"current-page":i.value,"total-records":b.value,"page-size":k.value,title:"Check-Off Reports","row-key":"loan_number","has-actions":!0,onPageChange:O,onSearch:N,onSort:V,onRowClick:z},{"header-actions":c(()=>[s("div",ue,[s("button",{onClick:q,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[m(y(ne),{class:"w-4 h-4 mr-2"}),o[2]||(o[2]=D(" Export "))])])]),"cell-customer":c(({row:t})=>[s("div",de,[s("div",fe,l(t.first_name)+" "+l(t.last_name),1),s("div",me,l(t.msisdn),1),s("div",ge,l(t.employee_number),1)])]),"cell-loan_number":c(({row:t})=>[s("div",be,l(t.loan_number),1)]),"cell-max_approved_loan_amount":c(({row:t})=>[s("div",pe,l(A(t.max_approved_loan_amount)),1)]),"cell-actual_balance":c(({row:t})=>[s("div",_e,l(A(t.actual_balance)),1)]),"cell-status":c(({row:t})=>[s("span",{class:Y([Q(t.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},l(H(t.status)),3)]),actions:c(({row:t,index:S})=>[s("div",ye,[s("button",{onClick:f=>M(S),class:"inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[m(y(ae),{class:"w-4 h-4"})],8,ve),u[S]?(_(),p("div",he,[s("div",xe,[s("button",{onClick:f=>w(t),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," View Details ",8,ke),s("button",{onClick:f=>j(t),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-900"}," Edit Account ",8,Ce),s("button",{onClick:f=>T(t),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-900"}," View Transactions ",8,we),t.status===1e3?(_(),p("button",{key:0,onClick:f=>L(t),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"}," Block Account ",8,Ae)):v("",!0),t.black_list_state===1?(_(),p("button",{key:1,onClick:f=>K(t),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"}," Unblock Account ",8,Se)):v("",!0)])])):v("",!0)])]),_:1},8,["data","loading","current-page","total-records","page-size"])]))}});export{ze as default};
