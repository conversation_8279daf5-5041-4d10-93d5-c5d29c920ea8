import{_ as te}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{_ as se}from"./NetworkDebugger.vue_vue_type_script_setup_true_lang-Bdso7PXR.js";import{o as y}from"./organizationsApi-CM42ShLT.js";import{r as oe}from"./BuildingOfficeIcon-Cd0d7gpT.js";import{r as ne}from"./PlusIcon-BKTWa0k6.js";import{d as ae,r as c,a as le,x as b,p as re,A as ie,c as m,b as e,B as ce,f as p,j as x,k as r,l as de,y as w,t as n,i as D,z as V,o as v}from"./index-DOaBqVmr.js";import"./clientsApi-DPSRLazl.js";import"./merchantsApi-DyJPmv37.js";const ue={class:"space-y-6"},ge={class:"flex items-center justify-between"},fe={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},ve={class:"bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200"},_e={class:"p-5"},me={class:"flex items-center"},xe={class:"flex-shrink-0"},he={class:"ml-5 w-0 flex-1"},ye={class:"text-lg font-medium text-gray-900"},be={class:"bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200"},pe={class:"p-5"},we={class:"flex items-center"},ke={class:"ml-5 w-0 flex-1"},Ce={class:"text-lg font-medium text-gray-900"},Oe={class:"bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200"},ze={class:"p-5"},Se={class:"flex items-center"},Le={class:"ml-5 w-0 flex-1"},De={class:"text-lg font-medium text-gray-900"},Ve={class:"bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200"},Me={class:"p-5"},Ae={class:"flex items-center"},$e={class:"ml-5 w-0 flex-1"},Be={class:"text-lg font-medium text-gray-900"},Ee={class:"font-medium text-gray-900"},Ie={class:"font-medium text-blue-600"},je={class:"text-sm"},Pe={class:"font-medium text-gray-900"},Fe={class:"text-gray-500"},Ne={class:"text-sm"},Re={class:"font-medium text-gray-900"},Te={class:"text-sm text-gray-900"},Ue={class:"text-sm"},qe={class:"font-medium text-gray-900"},He={class:"text-gray-500"},Qe={class:"text-sm text-gray-900"},Ge={class:"relative"},Je=["onClick"],Ke={key:0,class:"absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-md shadow-lg z-10"},We={class:"py-1"},Xe=["onClick"],Ye=["onClick"],Ze=["onClick"],et=["onClick"],tt=["onClick"],st=["onClick"],ot=["onClick"],nt=["onClick"],at=["onClick"],_t=ae({__name:"OrganizationsList",setup(lt){const d=c(!1),a=c([]),u=c(1),_=c(0),h=c(10),k=c(""),C=c(""),O=c("asc");c(!1);const l=le({}),M={client_account:"Account",client_name:"Organization Name",client_phone:"Contact Info",total_loan_assets:"Loan Assets",open_date:"Operating Hours",b2c_paybill:"PayBill Numbers",client_status:"Status",created:"Created Date"},A=b(()=>a.value.filter(t=>t.client_status===1).length),$=b(()=>a.value.filter(t=>t.client_status===0).length),B=b(()=>a.value.filter(t=>t.can_issue_loans==="1").length),E=!1,g=async(t={})=>{d.value=!0;try{const s={page:u.value,limit:h.value,offset:(u.value-1)*h.value,search:k.value,sortField:C.value,sortDirection:O.value,...t},f=await y.getOrganizations(s);f.status===200?(a.value=f.message.data||[],_.value=f.message.total_count||0,u.value=f.message.current_page||1):(console.error("Failed to fetch organizations:",f.message),a.value=[],_.value=0)}catch(s){console.error("Error fetching organizations:",s),a.value=[],_.value=0}finally{d.value=!1}},I=t=>{u.value=t,g()},j=t=>{k.value=t,u.value=1,g()},P=(t,s)=>{C.value=t,O.value=s,u.value=1,g()},F=t=>{console.log("Row clicked:",t)},N=()=>{g()},R=t=>{Object.keys(l).forEach(s=>{parseInt(s)!==t&&(l[parseInt(s)]=!1)}),l[t]=!l[t]},T=async t=>{try{d.value=!0;const s=await y.updateOrganization({client_account:t.client_account,client_status:1});s.status===200?(await g(),alert("Organization activated successfully")):alert("Failed to activate organization: "+s.message)}catch(s){console.error("Error activating organization:",s),alert("Error activating organization")}finally{d.value=!1,l[a.value.indexOf(t)]=!1}},U=async t=>{try{d.value=!0;const s=await y.updateOrganization({client_account:t.client_account,client_status:0});s.status===200?(await g(),alert("Organization deactivated successfully")):alert("Failed to deactivate organization: "+s.message)}catch(s){console.error("Error deactivating organization:",s),alert("Error deactivating organization")}finally{d.value=!1,l[a.value.indexOf(t)]=!1}},q=t=>{console.log("View loan accounts for:",t.client_name),l[a.value.indexOf(t)]=!1},H=t=>{console.log("View loan requests for:",t.client_name),l[a.value.indexOf(t)]=!1},Q=t=>{console.log("View loan limits for:",t.client_name),l[a.value.indexOf(t)]=!1},G=t=>{console.log("View transactions for:",t.client_name),l[a.value.indexOf(t)]=!1},J=t=>{console.log("View loan products for:",t.client_name),l[a.value.indexOf(t)]=!1},K=t=>{console.log("View system users for:",t.client_name),l[a.value.indexOf(t)]=!1},W=t=>{console.log("Send bulk SMS for:",t.client_name),l[a.value.indexOf(t)]=!1},X=t=>new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(t),z=t=>{const s=parseInt(t);return s===0||s<12?" AM":" PM"},Y=t=>{switch(t){case 1:return"bg-green-100 text-green-800";case 0:return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},Z=t=>{switch(t){case 1:return"Active";case 0:return"Inactive";default:return"Unknown"}},ee=t=>{const s=new Date(t);return s.toLocaleDateString()+" "+s.toLocaleTimeString()},S=()=>{Object.keys(l).forEach(t=>{l[parseInt(t)]=!1})};return re(()=>{g(),document.addEventListener("click",S)}),ie(()=>{document.removeEventListener("click",S)}),(t,s)=>{const f=de("router-link");return v(),m("div",ue,[e("div",ge,[s[1]||(s[1]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Organizations"),e("p",{class:"mt-1 text-sm text-gray-500"}," Manage and view all registered organizations ")],-1)),x(f,{to:{name:"organisations-add"},class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},{default:r(()=>[x(w(ne),{class:"h-4 w-4 mr-2"}),s[0]||(s[0]=D(" Add Organization "))]),_:1,__:[0]})]),e("div",fe,[e("div",ve,[e("div",_e,[e("div",me,[e("div",xe,[x(w(oe),{class:"h-6 w-6 text-gray-400"})]),e("div",he,[e("dl",null,[s[2]||(s[2]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Organizations",-1)),e("dd",ye,n(_.value),1)])])])])]),e("div",be,[e("div",pe,[e("div",we,[s[4]||(s[4]=e("div",{class:"flex-shrink-0"},[e("div",{class:"h-6 w-6 bg-green-100 rounded-full flex items-center justify-center"},[e("div",{class:"h-3 w-3 bg-green-500 rounded-full"})])],-1)),e("div",ke,[e("dl",null,[s[3]||(s[3]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Active",-1)),e("dd",Ce,n(A.value),1)])])])])]),e("div",Oe,[e("div",ze,[e("div",Se,[s[6]||(s[6]=e("div",{class:"flex-shrink-0"},[e("div",{class:"h-6 w-6 bg-red-100 rounded-full flex items-center justify-center"},[e("div",{class:"h-3 w-3 bg-red-500 rounded-full"})])],-1)),e("div",Le,[e("dl",null,[s[5]||(s[5]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Inactive",-1)),e("dd",De,n($.value),1)])])])])]),e("div",Ve,[e("div",Me,[e("div",Ae,[s[8]||(s[8]=e("div",{class:"flex-shrink-0"},[e("div",{class:"h-6 w-6 bg-blue-100 rounded-full flex items-center justify-center"},[e("div",{class:"h-3 w-3 bg-blue-500 rounded-full"})])],-1)),e("div",$e,[e("dl",null,[s[7]||(s[7]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Can Issue Loans",-1)),e("dd",Be,n(B.value),1)])])])])])]),w(E)?(v(),ce(se,{key:0})):p("",!0),x(te,{data:a.value,headers:M,loading:d.value,"current-page":u.value,"total-records":_.value,"page-size":h.value,title:"Organizations","row-key":"client_id","has-actions":!0,onPageChange:I,onSearch:j,onSort:P,onRowClick:F},{"header-actions":r(()=>[e("button",{onClick:N,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},s[9]||(s[9]=[e("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),D(" Refresh ")]))]),"cell-client_account":r(({value:o})=>[e("div",Ee,n(o),1)]),"cell-client_name":r(({value:o})=>[e("div",Ie,n(o),1)]),"cell-client_phone":r(({item:o})=>[e("div",je,[e("div",Pe,"+"+n(o.client_phone),1),e("div",Fe,n(o.client_email),1)])]),"cell-total_loan_assets":r(({item:o})=>[e("div",Ne,[e("div",Re,n(o.currency_code)+" "+n(X(o.total_loan_assets)),1),e("div",{class:V(["text-xs",o.can_issue_loans==="1"?"text-green-600":"text-orange-600"])},n(o.can_issue_loans_desc)+" loans ",3)])]),"cell-open_date":r(({item:o})=>[e("div",Te,n(o.open_date)+n(z(o.open_date))+" - "+n(o.close_date)+n(z(o.close_date)),1)]),"cell-b2c_paybill":r(({item:o})=>[e("div",Ue,[e("div",qe,"B2C: "+n(o.b2c_paybill),1),e("div",He,"C2B: "+n(o.c2b_paybill),1)])]),"cell-client_status":r(({value:o})=>[e("span",{class:V(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",Y(o)])},n(Z(o)),3)]),"cell-created":r(({value:o})=>[e("div",Qe,n(ee(o)),1)]),actions:r(({item:o,index:L})=>[e("div",Ge,[e("button",{onClick:i=>R(L),class:"inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},s[10]||(s[10]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})],-1)]),8,Je),l[L]?(v(),m("div",Ke,[e("div",We,[o.client_status!==1?(v(),m("button",{key:0,onClick:i=>T(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"}," Activate "+n(o.client_name),9,Xe)):(v(),m("button",{key:1,onClick:i=>U(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"}," Deactivate "+n(o.client_name),9,Ye)),e("button",{onClick:i=>q(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"}," View Loan Accounts ",8,Ze),e("button",{onClick:i=>H(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"}," View Loan Requests ",8,et),e("button",{onClick:i=>Q(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"}," View Loan Limits ",8,tt),e("button",{onClick:i=>G(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"}," View Transactions ",8,st),e("button",{onClick:i=>J(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"}," View Loan Products ",8,ot),e("button",{onClick:i=>K(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"}," View System Users ",8,nt),o.client_status===1?(v(),m("button",{key:2,onClick:i=>W(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"}," Send Bulk SMS ",8,at)):p("",!0)])])):p("",!0)])]),_:1},8,["data","loading","current-page","total-records","page-size"])])}}});export{_t as default};
