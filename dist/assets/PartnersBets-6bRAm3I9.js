import{d as I,r as a,a as Q,x,p as G,c as m,b as e,j as J,i as j,t as l,h as _,C as h,F as K,m as O,e as L,k as b,z as X,n as Y,o as v}from"./index-DOaBqVmr.js";import{_ as ee}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";const te={class:"space-y-6"},oe={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},se={class:"flex items-center justify-between"},ne={class:"flex space-x-3"},re=["disabled"],le={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ae={key:1,class:"-ml-1 mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ie={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},de={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},ue={class:"flex items-center"},ce={class:"ml-4"},pe={class:"text-2xl font-semibold text-gray-900"},me={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},be={class:"flex items-center"},ve={class:"ml-4"},ge={class:"text-2xl font-semibold text-gray-900"},fe={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},xe={class:"flex items-center"},_e={class:"ml-4"},he={class:"text-2xl font-semibold text-gray-900"},ye={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},we={class:"flex items-center"},ke={class:"ml-4"},Ce={class:"text-2xl font-semibold text-gray-900"},Be={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},Se={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Ve=["value"],Te={class:"text-gray-900 font-medium"},Pe={class:"text-gray-900 font-medium"},Me={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},je={class:"flex items-center space-x-2"},Le=["onClick"],ze=["onClick"],Ue=I({__name:"PartnersBets",setup(Ae){const z=Y(),i=a(!1),d=a([]),C=a([]),p=a(1),B=a(0),y=a(10),w=a(""),g=a(""),S=a("asc"),r=Q({partner_id:"",bet_status:"",sport_type:"",date_range:""}),A=[{bet_id:"BET001",partner_name:"SportsBet Pro",partner_id:1,customer_id:"CUST001",sport_type:"football",event_name:"Manchester United vs Liverpool",bet_type:"match_winner",bet_amount:100,odds:"2.50",potential_payout:250,bet_status:"pending",placed_at:"2024-01-31T14:30:00Z",settled_at:null},{bet_id:"BET002",partner_name:"SportsBet Pro",partner_id:1,customer_id:"CUST002",sport_type:"basketball",event_name:"Lakers vs Warriors",bet_type:"over_under",bet_amount:50,odds:"1.85",potential_payout:92.5,bet_status:"won",placed_at:"2024-01-31T12:15:00Z",settled_at:"2024-01-31T16:45:00Z"},{bet_id:"BET003",partner_name:"BetMaster",partner_id:2,customer_id:"CUST003",sport_type:"tennis",event_name:"Djokovic vs Nadal",bet_type:"match_winner",bet_amount:200,odds:"1.75",potential_payout:350,bet_status:"lost",placed_at:"2024-01-31T10:00:00Z",settled_at:"2024-01-31T13:30:00Z"}],D=[{id:1,name:"SportsBet Pro"},{id:2,name:"BetMaster"},{id:3,name:"WinBig Casino"}],k=x(()=>d.value.length),R=x(()=>d.value.reduce((o,t)=>o+(t.bet_amount||0),0)),U=x(()=>new Set(d.value.map(o=>o.partner_id)).size),E=x(()=>{const o=d.value.filter(t=>t.bet_status==="won").length;return k.value>0?Math.round(o/k.value*100):0}),u=async()=>{i.value=!0;try{await new Promise(n=>setTimeout(n,1e3));let o=[...A];if(r.partner_id&&(o=o.filter(n=>n.partner_id===parseInt(r.partner_id))),r.bet_status&&(o=o.filter(n=>n.bet_status===r.bet_status)),r.sport_type&&(o=o.filter(n=>n.sport_type===r.sport_type)),w.value){const n=w.value.toLowerCase();o=o.filter(c=>c.bet_id.toLowerCase().includes(n)||c.partner_name.toLowerCase().includes(n)||c.event_name.toLowerCase().includes(n)||c.customer_id.toLowerCase().includes(n))}g.value&&o.sort((n,c)=>{const T=n[g.value],P=c[g.value],M=S.value==="asc"?1:-1;return T<P?-1*M:T>P?1*M:0}),B.value=o.length;const t=(p.value-1)*y.value,s=t+y.value;d.value=o.slice(t,s),C.value=D}catch(o){console.error("Error loading partners bets:",o)}finally{i.value=!1}},H=()=>{u()},$=o=>{p.value=o,u()},F=o=>{w.value=o,p.value=1,u()},N=(o,t)=>{g.value=o,S.value=t,u()},W=o=>{V(o)},f=()=>{p.value=1,u()},V=o=>{console.log("View bet:",o)},Z=o=>{console.log("View bet slip:",o),z.push({name:"partners-bet-slips",query:{bet_id:o.bet_id}})},q=()=>{console.log("Export partners bets data")};return G(()=>{u()}),(o,t)=>(v(),m("div",te,[e("div",oe,[e("div",se,[t[7]||(t[7]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Partners Bets"),e("p",{class:"mt-1 text-sm text-gray-600"}," Monitor and analyze betting activity across all partners ")],-1)),e("div",ne,[e("button",{onClick:H,disabled:i.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"},[i.value?(v(),m("svg",le,t[4]||(t[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):(v(),m("svg",ae,t[5]||(t[5]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))),j(" "+l(i.value?"Refreshing...":"Refresh"),1)],8,re),e("button",{onClick:q,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},t[6]||(t[6]=[e("svg",{class:"-ml-1 mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),j(" Export ")]))])])]),e("div",ie,[e("div",de,[e("div",ue,[t[9]||(t[9]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),e("div",ce,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-500"},"Total Bets",-1)),e("p",pe,l(k.value.toLocaleString()),1)])])]),e("div",me,[e("div",be,[t[11]||(t[11]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-100 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])])],-1)),e("div",ve,[t[10]||(t[10]=e("p",{class:"text-sm font-medium text-gray-500"},"Total Volume",-1)),e("p",ge,"$"+l(R.value.toLocaleString()),1)])])]),e("div",fe,[e("div",xe,[t[13]||(t[13]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])])],-1)),e("div",_e,[t[12]||(t[12]=e("p",{class:"text-sm font-medium text-gray-500"},"Active Partners",-1)),e("p",he,l(U.value),1)])])]),e("div",ye,[e("div",we,[t[15]||(t[15]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",ke,[t[14]||(t[14]=e("p",{class:"text-sm font-medium text-gray-500"},"Win Rate",-1)),e("p",Ce,l(E.value)+"%",1)])])])]),e("div",Be,[e("div",Se,[e("div",null,[t[17]||(t[17]=e("label",{for:"partner-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Partner",-1)),_(e("select",{id:"partner-filter","onUpdate:modelValue":t[0]||(t[0]=s=>r.partner_id=s),onChange:f,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},[t[16]||(t[16]=e("option",{value:""},"All Partners",-1)),(v(!0),m(K,null,O(C.value,s=>(v(),m("option",{key:s.id,value:s.id},l(s.name),9,Ve))),128))],544),[[h,r.partner_id]])]),e("div",null,[t[19]||(t[19]=e("label",{for:"status-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Bet Status",-1)),_(e("select",{id:"status-filter","onUpdate:modelValue":t[1]||(t[1]=s=>r.bet_status=s),onChange:f,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},t[18]||(t[18]=[L('<option value="">All Statuses</option><option value="pending">Pending</option><option value="won">Won</option><option value="lost">Lost</option><option value="cancelled">Cancelled</option>',5)]),544),[[h,r.bet_status]])]),e("div",null,[t[21]||(t[21]=e("label",{for:"sport-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Sport",-1)),_(e("select",{id:"sport-filter","onUpdate:modelValue":t[2]||(t[2]=s=>r.sport_type=s),onChange:f,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},t[20]||(t[20]=[L('<option value="">All Sports</option><option value="football">Football</option><option value="basketball">Basketball</option><option value="tennis">Tennis</option><option value="cricket">Cricket</option>',5)]),544),[[h,r.sport_type]])]),e("div",null,[t[23]||(t[23]=e("label",{for:"date-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Date Range",-1)),_(e("select",{id:"date-filter","onUpdate:modelValue":t[3]||(t[3]=s=>r.date_range=s),onChange:f,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},t[22]||(t[22]=[e("option",{value:""},"All Time",-1),e("option",{value:"today"},"Today",-1),e("option",{value:"week"},"This Week",-1),e("option",{value:"month"},"This Month",-1)]),544),[[h,r.date_range]])])])]),J(ee,{data:d.value,loading:i.value,"current-page":p.value,"total-records":B.value,"page-size":y.value,title:"Partners Betting Activity","row-key":"bet_id","has-actions":!0,onPageChange:$,onSearch:F,onSort:N,onRowClick:W},{"cell-bet_status":b(({value:s})=>[e("span",{class:X([{"bg-yellow-100 text-yellow-800":s==="pending","bg-green-100 text-green-800":s==="won","bg-red-100 text-red-800":s==="lost","bg-gray-100 text-gray-800":s==="cancelled"},"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},l(s?s.charAt(0).toUpperCase()+s.slice(1):"-"),3)]),"cell-bet_amount":b(({value:s})=>[e("span",Te," $"+l(s?s.toLocaleString():"0"),1)]),"cell-potential_payout":b(({value:s})=>[e("span",Pe," $"+l(s?s.toLocaleString():"0"),1)]),"cell-odds":b(({value:s})=>[e("span",Me,l(s||"-"),1)]),actions:b(({item:s})=>[e("div",je,[e("button",{onClick:n=>V(s),class:"text-blue-600 hover:text-blue-900 text-sm font-medium transition-colors duration-200"}," View ",8,Le),e("button",{onClick:n=>Z(s),class:"text-indigo-600 hover:text-indigo-900 text-sm font-medium transition-colors duration-200"}," Bet Slip ",8,ze)])]),_:1},8,["data","loading","current-page","total-records","page-size"])]))}});export{Ue as default};
