import{d as J,u as W,r as v,p as X,c as i,b as r,j as f,i as S,y as a,k as l,f as g,t as c,z as Y,n as Z,o as u}from"./index-DOaBqVmr.js";import{_ as ee}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{_ as te}from"./FilterCards.vue_vue_type_script_setup_true_lang-Bk3N6i80.js";import{l as x}from"./loanApi-CajqAy4f.js";import{u as se}from"./useBackofficeActions-O4W2PmYt.js";import{r as re}from"./ArrowPathIcon-BCh5HUKO.js";import{r as oe}from"./EllipsisVerticalIcon-BCCOPEML.js";import{r as ae}from"./ArrowDownTrayIcon-BWjpub36.js";const ne={class:"space-y-6"},le={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},ie={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},ce={class:"mt-4 sm:mt-0 flex space-x-3"},ue={class:"flex space-x-2"},de={class:"text-sm"},me={class:"font-medium text-gray-900"},fe={class:"text-gray-500"},ge={class:"text-sm font-medium text-gray-900"},pe={class:"text-sm font-medium text-blue-600"},ye={class:"text-sm text-gray-900"},_e={class:"relative"},ve=["onClick"],he={key:0,class:"absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},xe={class:"py-1"},be=["onClick"],ke=["onClick"],Ce=["onClick"],we=["onClick"],Le=["onClick"],Pe=J({__name:"LoanLimits",setup($e){Z();const b=W(),{showDropdown:D,loading:n,currentPage:p,pageSize:k,totalRecords:y,searchQuery:j,toggleDropdown:F,goToPage:A,handleSearch:R}=se(),_=v([]),E=v(""),q=v("asc"),d=v({client_id:b.selectedClientId||"",status:"",reference_id:"",start_date:"",end_date:""}),P=[{value:"1",label:"Approved"},{value:"2",label:"Pending"},{value:"3",label:"Rejected"}],z=[{key:"reference_id",label:"Reference ID",sortable:!0},{key:"merchant_info",label:"Organization",sortable:!1},{key:"current_limit",label:"Current Limit",sortable:!0},{key:"requested_limit",label:"Requested Limit",sortable:!0},{key:"status",label:"Status",sortable:!0},{key:"created_at",label:"Date",sortable:!0}],o=async()=>{var e,s;n.value=!0;try{const t=await x.getLoanLimits({page:p.value,limit:k.value,search:j.value,...d.value});t.status===200?(_.value=((e=t.message)==null?void 0:e.data)||[],y.value=((s=t.message)==null?void 0:s.total_count)||0):(_.value=[],y.value=0)}catch(t){console.error("Error fetching loan limits:",t),_.value=[],y.value=0}finally{n.value=!1}},V=e=>{Object.assign(d.value,e)},N=e=>{A(e,o)},B=e=>{R(e,o)},I=(e,s)=>{E.value=e,q.value=s,o()},M=e=>{C(e)},O=()=>{o()},T=e=>{d.value={...e},p.value=1,o()},U=()=>{d.value={client_id:b.selectedClientId||"",status:"",reference_id:"",start_date:"",end_date:""},p.value=1,o()},C=e=>{console.log("View details for limit:",e.reference_id)},w=async e=>{try{if(!confirm(`Are you sure you want to approve limit request ${e.reference_id} for ${h(e.requested_limit)}?`))return;n.value=!0;const t=await x.approveLoanLimit(e.reference_id,1,`Limit of ${e.reference_id} approved`);t.status===200?(alert("Loan limit approved successfully"),o()):alert(`Failed to approve loan limit: ${t.message}`)}catch(s){console.error("Error approving loan limit:",s),alert("Failed to approve loan limit. Please try again.")}finally{n.value=!1}},L=async e=>{try{if(!confirm(`Are you sure you want to reject limit request ${e.reference_id}?`))return;n.value=!0;const t=await x.approveLoanLimit(e.reference_id,3,`Limit request ${e.reference_id} rejected`);t.status===200?(alert("Loan limit rejected successfully"),o()):alert(`Failed to reject loan limit: ${t.message}`)}catch(s){console.error("Error rejecting loan limit:",s),alert("Failed to reject loan limit. Please try again.")}finally{n.value=!1}},K=()=>{console.log("Export loan limits data")},h=e=>{const s=typeof e=="string"?parseFloat(e):e;return new Intl.NumberFormat("en-KE",{style:"currency",currency:"KES",minimumFractionDigits:0,maximumFractionDigits:0}).format(s)},H=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),Q=e=>({1:"Approved",2:"Pending",3:"Rejected"})[e]||"Unknown",G=e=>({1:"bg-green-100 text-green-800",2:"bg-yellow-100 text-yellow-800",3:"bg-red-100 text-red-800"})[e]||"bg-gray-100 text-gray-800";return X(()=>{o()}),(e,s)=>(u(),i("div",ne,[r("div",le,[r("div",ie,[s[1]||(s[1]=r("div",null,[r("h1",{class:"text-2xl font-bold text-gray-900"},"Loan Limits"),r("p",{class:"text-gray-600 mt-1"},"Configure and manage loan limits for customers")],-1)),r("div",ce,[r("button",{onClick:O,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[f(a(re),{class:"w-4 h-4 mr-2"}),s[0]||(s[0]=S(" Refresh "))])])])]),f(te,{filters:d.value,"filter-type":"limits","status-options":P,"onUpdate:filters":V,onApply:T,onClear:U},null,8,["filters"]),f(ee,{data:_.value,headers:z,loading:a(n),"current-page":a(p),"total-records":a(y),"page-size":a(k),title:"Loan Limits","row-key":"reference_id","has-actions":!0,onPageChange:N,onSearch:B,onSort:I,onRowClick:M},{"header-actions":l(()=>[r("div",ue,[r("button",{onClick:K,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[f(a(ae),{class:"w-4 h-4 mr-2"}),s[2]||(s[2]=S(" Export "))])])]),"cell-merchant_info":l(({row:t})=>[r("div",de,[r("div",me,c(t.merchant_name),1),r("div",fe,"Emp: "+c(t.employee_number),1)])]),"cell-current_limit":l(({row:t})=>[r("div",ge,c(h(t.current_limit)),1)]),"cell-requested_limit":l(({row:t})=>[r("div",pe,c(h(t.requested_limit)),1)]),"cell-status":l(({row:t})=>[r("span",{class:Y([G(t.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},c(Q(t.status)),3)]),"cell-created_at":l(({row:t})=>[r("div",ye,c(H(t.created_at)),1)]),actions:l(({row:t,index:$})=>[r("div",_e,[r("button",{onClick:m=>a(F)($),class:"inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[f(a(oe),{class:"w-4 h-4"})],8,ve),a(D)[$]?(u(),i("div",he,[r("div",xe,[r("button",{onClick:m=>C(t),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," View Details ",8,be),t.status===2?(u(),i("button",{key:0,onClick:m=>w(t),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"}," Approve ",8,ke)):g("",!0),t.status===2?(u(),i("button",{key:1,onClick:m=>L(t),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"}," Reject ",8,Ce)):g("",!0),t.status===2?(u(),i("button",{key:2,onClick:m=>w(t),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"}," Approve ",8,we)):g("",!0),t.status===2?(u(),i("button",{key:3,onClick:m=>L(t),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"}," Reject ",8,Le)):g("",!0)])])):g("",!0)])]),_:1},8,["data","loading","current-page","total-records","page-size"])]))}});export{Pe as default};
