import{K as l,G as g}from"./index-DOaBqVmr.js";const f={async getLoanRequests(t={}){var n,r;try{const s=localStorage.getItem("selectedClientId"),a={limit:t.limit||10,offset:t.offset||1,page:t.page||1,client_id:t.client_id||s,status:t.status||"",loan_number:t.loan_number||"",reference_id:t.reference_id||"",phone_number:t.phone_number||"",start_date:t.start_date||"",end_date:t.end_date||"",amount:t.amount||"",...t},o=l(a),e=(await g.post("merchant/v1/view/loan_request",a,{headers:{"X-Hash-Key":o}})).data.data;return{status:e.code===200?200:e.code,message:{data:e.data||[],total_count:e.total_count||0,current_page:e.current_page||1,per_page:e.per_page||a.limit},code:e.code.toString()}}catch(s){if(console.error("Error fetching loan requests:",s),s.response){const a=s.response.status,o=s.response.data;return{status:((n=o.data)==null?void 0:n.code)||a,message:{data:[],total_count:0,current_page:1,per_page:t.limit||10},code:(((r=o.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:t.limit||10},code:"500"}}},async approveLoanRequest(t,n,r,s){var a,o,c,e,i,d;try{const u={id:t,status:n,approved_amount:r,status_description:s},_=l(u),p=await g.post(`merchant/v1/approve_loan/${t}`,u,{headers:{"X-Hash-Key":_}});return{status:p.data.data.code===200?200:p.data.data.code,message:p.data.data.message||"Loan request processed successfully",code:p.data.data.code.toString()}}catch(u){if(console.error("Error processing loan request:",u),((a=u.response)==null?void 0:a.status)===401)throw new Error("Unauthorized access");return{status:((o=u.response)==null?void 0:o.status)||500,message:((i=(e=(c=u.response)==null?void 0:c.data)==null?void 0:e.data)==null?void 0:i.message)||"Failed to process loan request",code:(((d=u.response)==null?void 0:d.status)||500).toString()}}},async resendTAN(t){var n,r,s,a,o;try{const c={request_number:t},e=l(c),i=await g.post("merchant/v1/resend_tan",c,{headers:{"X-Hash-Key":e}});return{status:i.data.data.code===200?200:i.data.data.code,message:i.data.data.message||"TAN sent successfully",code:i.data.data.code.toString()}}catch(c){return console.error("Error resending TAN:",c),{status:((n=c.response)==null?void 0:n.status)||500,message:((a=(s=(r=c.response)==null?void 0:r.data)==null?void 0:s.data)==null?void 0:a.message)||"Failed to resend TAN",code:(((o=c.response)==null?void 0:o.status)||500).toString()}}},async approveLoanLimit(t,n,r){var s,a,o,c,e,i;try{const d={reference_id:t,status:n,narration:r},u=l(d),_=await g.post(`merchant/v1/approve_limits/${t}`,d,{headers:{"X-Hash-Key":u}});return{status:_.data.data.code===200?200:_.data.data.code,message:_.data.data.message||"Loan limit processed successfully",code:_.data.data.code.toString()}}catch(d){if(console.error("Error processing loan limit:",d),((s=d.response)==null?void 0:s.status)===401)throw new Error("Unauthorized access");return{status:((a=d.response)==null?void 0:a.status)||500,message:((e=(c=(o=d.response)==null?void 0:o.data)==null?void 0:c.data)==null?void 0:e.message)||"Failed to process loan limit",code:(((i=d.response)==null?void 0:i.status)||500).toString()}}},async getLoanLimits(t={}){var n,r;try{const s=localStorage.getItem("selectedClientId"),a={limit:t.limit||10,offset:t.offset||1,page:t.page||1,client_id:t.client_id||s,status:t.status||"",reference_id:t.reference_id||"",start_date:t.start_date||"",end_date:t.end_date||"",...t},o=l(a),e=(await g.post("merchant/v1/view/loan_limits",a,{headers:{"X-Hash-Key":o}})).data.data;return{status:e.code===200?200:e.code,message:{data:e.data||[],total_count:e.total_count||0,current_page:e.current_page||1,per_page:e.per_page||a.limit},code:e.code.toString()}}catch(s){if(console.error("Error fetching loan limits:",s),s.response){const a=s.response.status,o=s.response.data;return{status:((n=o.data)==null?void 0:n.code)||a,message:{data:[],total_count:0,current_page:1,per_page:t.limit||10},code:(((r=o.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:t.limit||10},code:"500"}}},async getLoanAccounts(t={}){var n,r;try{const s=localStorage.getItem("selectedClientId"),a={limit:t.limit||10,offset:t.offset||1,page:t.page||1,client_id:t.client_id||s,status:t.status||"",loan_number:t.loan_number||"",msisdn:t.msisdn||"",national_id:t.national_id||"",start_date:t.start_date||"",end_date:t.end_date||"",...t},o=l(a),e=(await g.post("merchant/v1/view/loan_accounts",a,{headers:{"X-Hash-Key":o}})).data.data;return{status:e.code===200?200:e.code,message:{data:e.data||[],total_count:e.total_count||0,current_page:e.current_page||1,per_page:e.per_page||a.limit},code:e.code.toString()}}catch(s){if(console.error("Error fetching loan accounts:",s),s.response){const a=s.response.status,o=s.response.data;return{status:((n=o.data)==null?void 0:n.code)||a,message:{data:[],total_count:0,current_page:1,per_page:t.limit||10},code:(((r=o.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:t.limit||10},code:"500"}}},async getLoanRepayments(t={}){var n,r;try{const s=localStorage.getItem("selectedClientId"),a={limit:t.limit||10,offset:t.offset||1,page:t.page||1,client_id:t.client_id||s,loan_number:t.loan_number||"",request_number:t.request_number||"",start_date:t.start_date||"",end_date:t.end_date||"",payment_method:t.payment_method||"",...t},o=l(a),e=(await g.post("merchant/v1/view/loan_repayments",a,{headers:{"X-Hash-Key":o}})).data.data;return{status:e.code===200?200:e.code,message:{data:e.data||[],total_count:e.total_count||0,current_page:e.current_page||1,per_page:e.per_page||a.limit},code:e.code.toString()}}catch(s){if(console.error("Error fetching loan repayments:",s),s.response){const a=s.response.status,o=s.response.data;return{status:((n=o.data)==null?void 0:n.code)||a,message:{data:[],total_count:0,current_page:1,per_page:t.limit||10},code:(((r=o.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:t.limit||10},code:"500"}}},async getLoanProducts(t={}){var n,r;try{const s=localStorage.getItem("selectedClientId"),a={limit:t.limit||10,offset:t.offset||1,page:t.page||1,client_id:t.client_id||s,...t},o=l(a),e=(await g.post("merchant/v1/view/loan_products",a,{headers:{"X-Hash-Key":o}})).data.data;return{status:e.code===200?200:e.code,message:{data:e.data||[],total_count:e.total_count||0,current_page:e.current_page||1,per_page:e.per_page||a.limit},code:e.code.toString()}}catch(s){if(console.error("Error fetching loan products:",s),s.response){const a=s.response.status,o=s.response.data;return{status:((n=o.data)==null?void 0:n.code)||a,message:{data:[],total_count:0,current_page:1,per_page:t.limit||10},code:(((r=o.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:t.limit||10},code:"500"}}},async getCheckOffReports(t={}){var n,r;try{const s=localStorage.getItem("selectedClientId"),a={limit:t.limit||10,offset:t.offset||1,page:t.page||1,client_id:t.client_id||s,type_id:t.type_id||"",start:t.start_date||"",end:t.end_date||"",...t},o=l(a),e=(await g.post("checkoff/v1/report",a,{headers:{"X-Hash-Key":o}})).data.data;return{status:e.code===200?200:e.code,message:{data:e.data||[],total_count:e.total_count||0,current_page:e.current_page||1,per_page:e.per_page||a.limit},code:e.code.toString()}}catch(s){if(console.error("Error fetching check-off reports:",s),s.response){const a=s.response.status,o=s.response.data;return{status:((n=o.data)==null?void 0:n.code)||a,message:{data:[],total_count:0,current_page:1,per_page:t.limit||10},code:(((r=o.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:t.limit||10},code:"500"}}}};export{f as l};
