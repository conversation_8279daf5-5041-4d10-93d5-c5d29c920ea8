import{c as i,b as e,o as l,d as N,r as u,p as B,j as f,i as b,y as c,f as w,h as g,v as h,C as k,F as j,m as A,t as n,z as q,g as L,q as T,n as F}from"./index-DOaBqVmr.js";import{s as P}from"./systemApi-CvVfCghC.js";import{P as O}from"./permissions-vZl7iLZK.js";import{r as R}from"./ArrowLeftIcon-DKfvydGS.js";import{r as z}from"./ShieldCheckIcon-ClBEPwdF.js";function Z(_,d){return l(),i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"})])}const $={class:"space-y-6"},H={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},G={class:"flex items-center justify-between"},J={key:0,class:"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center"},K={key:1,class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},Q={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},W=["value"],X={class:"md:col-span-2"},Y=["value"],ee={key:0},se={class:"bg-blue-50 rounded-lg p-4"},te={class:"flex items-start space-x-3"},oe={class:"mt-2 text-sm text-blue-800"},re={class:"mt-1 list-disc list-inside space-y-1"},ne={key:1},ie={class:"bg-gray-50 rounded-lg p-4"},le={class:"flex items-start space-x-4"},ae={class:"flex-1"},de={class:"font-medium text-gray-900"},ue={class:"text-sm text-gray-600 mt-1"},me={class:"flex items-center mt-2 space-x-4"},pe={class:"text-xs text-gray-500"},ce={class:"font-medium"},ge={class:"text-xs text-gray-500"},fe={class:"font-medium"},be={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200"},xe=["disabled"],Pe=N({__name:"EditPermission",setup(_){const d=F(),M=T(),m=u(!1),x=u(!0),a=u(null),t=u({id:0,name:"",description:"",module:"",status:1}),p=u(null),U=Object.values(O),C=async()=>{var s;const r=M.params.id;if(!r){d.push({name:"system-permissions"});return}try{const o=await P.getPermissions({limit:1e3});if(o.status===200){const V=((s=o.message)==null?void 0:s.data)||[];a.value=V.find(D=>D.id===parseInt(r))||null,a.value?(t.value={id:a.value.id,name:a.value.name,description:a.value.description||"",module:a.value.module||"",status:a.value.status},p.value={roles:Math.floor(Math.random()*5)+1,users:Math.floor(Math.random()*20)+1}):d.push({name:"system-permissions"})}}catch(o){console.error("Error fetching permission:",o),d.push({name:"system-permissions"})}finally{x.value=!1}},I=async()=>{if(!t.value.name.trim()){alert("Permission name is required");return}if(confirm("Are you sure you want to update this permission?")){m.value=!0;try{const r=await P.updatePermission({id:t.value.id,name:t.value.name.trim(),description:t.value.description.trim(),module:t.value.module,status:t.value.status});r.status===200?(alert("Permission updated successfully!"),d.push({name:"system-permissions"})):alert("Error updating permission: "+r.message)}catch(r){console.error("Error updating permission:",r),alert("Error updating permission")}finally{m.value=!1}}},v=()=>{d.push({name:"system-permissions"})},y=r=>r.charAt(0).toUpperCase()+r.slice(1).replace(/[_-]/g," "),S=r=>({1:"Active",0:"Inactive"})[r]||"Unknown",E=r=>({1:"bg-green-100 text-green-800",0:"bg-red-100 text-red-800"})[r]||"bg-gray-100 text-gray-800";return B(()=>{C()}),(r,s)=>(l(),i("div",$,[e("div",H,[e("div",G,[s[5]||(s[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Edit Permission"),e("p",{class:"text-gray-600 mt-1"},"Update permission information and settings")],-1)),e("button",{onClick:v,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[f(c(R),{class:"w-4 h-4 mr-2"}),s[4]||(s[4]=b(" Back to Permissions "))])])]),x.value?(l(),i("div",J,s[6]||(s[6]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"},null,-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Loading permission data...",-1)]))):(l(),i("div",K,[e("form",{onSubmit:L(I,["prevent"]),class:"space-y-6"},[e("div",null,[s[16]||(s[16]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Basic Information",-1)),e("div",Q,[e("div",null,[s[7]||(s[7]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Permission Name *",-1)),g(e("input",{"onUpdate:modelValue":s[0]||(s[0]=o=>t.value.name=o),type:"text",required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"e.g., users.view, loans.approve"},null,512),[[h,t.value.name]]),s[8]||(s[8]=e("p",{class:"mt-1 text-sm text-gray-500"},"Use dot notation for hierarchical permissions",-1))]),e("div",null,[s[10]||(s[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Module *",-1)),g(e("select",{"onUpdate:modelValue":s[1]||(s[1]=o=>t.value.module=o),required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},[s[9]||(s[9]=e("option",{value:""},"Select Module",-1)),(l(!0),i(j,null,A(c(U),o=>(l(),i("option",{key:o,value:o},n(y(o)),9,W))),128))],512),[[k,t.value.module]])]),e("div",X,[s[11]||(s[11]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Description",-1)),g(e("textarea",{"onUpdate:modelValue":s[2]||(s[2]=o=>t.value.description=o),rows:"3",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Describe what this permission allows users to do"},null,512),[[h,t.value.description]])]),e("div",null,[s[13]||(s[13]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Status",-1)),g(e("select",{"onUpdate:modelValue":s[3]||(s[3]=o=>t.value.status=o),class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},s[12]||(s[12]=[e("option",{value:1},"Active",-1),e("option",{value:0},"Inactive",-1)]),512),[[k,t.value.status]])]),e("div",null,[s[14]||(s[14]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Permission ID",-1)),e("input",{value:t.value.id,type:"text",disabled:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm"},null,8,Y),s[15]||(s[15]=e("p",{class:"mt-1 text-sm text-gray-500"},"System-generated ID (read-only)",-1))])])]),p.value?(l(),i("div",ee,[s[20]||(s[20]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Permission Usage",-1)),e("div",se,[e("div",te,[f(c(Z),{class:"w-5 h-5 text-blue-600 mt-0.5"}),e("div",null,[s[19]||(s[19]=e("h4",{class:"text-sm font-medium text-blue-900"},"Usage Information",-1)),e("div",oe,[s[17]||(s[17]=e("p",null,"This permission is currently assigned to:",-1)),e("ul",re,[e("li",null,n(p.value.roles)+" role(s)",1),e("li",null,n(p.value.users)+" user(s)",1)]),s[18]||(s[18]=e("p",{class:"mt-2 text-xs text-blue-700"}," Changes to this permission will affect all assigned users and roles. ",-1))])])])])])):w("",!0),t.value.name&&t.value.module?(l(),i("div",ne,[s[23]||(s[23]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Permission Preview",-1)),e("div",ie,[e("div",le,[f(c(z),{class:"w-8 h-8 text-blue-600 mt-1"}),e("div",ae,[e("h4",de,n(t.value.name),1),e("p",ue,n(t.value.description||"No description provided"),1),e("div",me,[e("span",pe,[s[21]||(s[21]=b("Module: ")),e("span",ce,n(y(t.value.module)),1)]),e("span",ge,[s[22]||(s[22]=b("ID: ")),e("span",fe,n(t.value.id),1)]),e("span",{class:q([E(t.value.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},n(S(t.value.status)),3)])])])])])):w("",!0),e("div",be,[e("button",{type:"button",onClick:v,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Cancel "),e("button",{type:"submit",disabled:m.value||!t.value.name||!t.value.module,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},n(m.value?"Updating...":"Update Permission"),9,xe)])],32)]))]))}});export{Pe as default};
