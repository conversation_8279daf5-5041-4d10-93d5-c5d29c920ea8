import{d as F,u as H,r as i,x as S,p as K,c as r,b as s,j as Y,i as w,y as V,f as b,t as d,h as a,v as u,F as v,m as f,C as _,g as $,n as z,o as n}from"./index-DOaBqVmr.js";import{s as L}from"./systemApi-CvVfCghC.js";import{c as G}from"./clientsApi-DPSRLazl.js";import{r as J}from"./ArrowLeftIcon-DKfvydGS.js";const Q={class:"space-y-6"},W={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},X={class:"flex items-center justify-between"},Z={key:0,class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},ee={key:1,class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},se={key:0,class:"space-y-2"},te={class:"block text-sm font-medium text-gray-700"},le={key:0,class:"text-gray-500"},oe={class:"relative"},re=["placeholder"],ne={key:0,class:"absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"},ae=["onClick"],ie={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},de={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ue={class:"flex"},me={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},ce=["value"],be=["value"],ve={class:"space-y-6"},fe=["value"],pe={key:0,class:"mt-1 text-sm text-gray-500"},ge={key:0,class:"space-y-4"},ye={class:"bg-gray-50 rounded-lg p-4 max-h-40 overflow-y-auto"},xe={class:"grid grid-cols-1 md:grid-cols-2 gap-2"},we={class:"flex items-center justify-end space-x-3 pt-6 border-t border-gray-200"},_e=["disabled"],ke={key:0,class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},he={key:1,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},De=F({__name:"AddUser",setup(Ae){const U=z(),k=H(),p=i(!1),I=i(!0),g=i([]),h=i([]),A=i(""),y=i(!1),N=i("Select Organisation"),x=i(""),t=i({dial_code:"254",msisdn:"",email_address:"",first_name:"",middle_name:"",last_name:"",role_id:"",national_id:"",nationality:"KENYA",identifier_type:"NATIONAL_ID",account_number:""}),R=[{text:"KENYA",value:"KENYA"}],P=[{text:"NATIONAL ID",value:"NATIONAL_ID"},{text:"HUDUMA ID",value:"HUDUMA_ID"},{text:"PASSPORT",value:"PASSPORT"},{text:"ALIEN ID",value:"ALIEN_ID"}],m=S(()=>g.value.find(o=>o.role_id===parseInt(t.value.role_id))),C=S(()=>t.value.first_name&&t.value.last_name&&t.value.email_address&&t.value.msisdn&&t.value.role_id&&t.value.national_id&&t.value.nationality&&t.value.identifier_type),q=o=>{x.value=o.text,t.value.account_number=o.value,y.value=!1,A.value="",N.value=x.value},M=()=>{y.value=!y.value},T=()=>{},B=async()=>{try{const o=await G.getClients({limit:100});o.status===200&&(h.value=o.message.data.map(e=>({text:e.client_name,value:e.client_account})))}catch(o){console.error("Error fetching organisations:",o),h.value=[]}},O=async()=>{var o;try{const e=await L.getRoles({limit:100});e.status===200&&(g.value=((o=e.message)==null?void 0:o.data)||[])}catch(e){console.error("Error fetching roles:",e),g.value=[]}},j=async()=>{var o,e;if(!C.value){alert("Please fill in all required fields");return}if(confirm("Are you sure you want to create this user?")){p.value=!0;try{const c={username:`${t.value.first_name}_${t.value.last_name}`.toLowerCase(),email_address:t.value.email_address,msisdn:t.value.msisdn,role_id:parseInt(t.value.role_id),permissions:((e=(o=m.value)==null?void 0:o.permissions)==null?void 0:e.map(E=>E.id))||[],client_id:t.value.account_number||void 0,first_name:t.value.first_name,middle_name:t.value.middle_name,last_name:t.value.last_name,national_id:t.value.national_id,nationality:t.value.nationality,identifier_type:t.value.identifier_type},l=await L.createUser(c);l.status===200?(alert("User created successfully!"),U.push({name:"system-users"})):alert("Error creating user: "+l.message)}catch(c){console.error("Error creating user:",c),alert("Error creating user")}finally{p.value=!1}}},D=()=>{U.push({name:"system-users"})};return K(async()=>{var o;k.isSuperUser||(t.value.account_number=((o=k.user)==null?void 0:o.client_account)||""),await Promise.all([B(),O()]),I.value=!1}),(o,e)=>{var c;return n(),r("div",Q,[s("div",W,[s("div",X,[e[12]||(e[12]=s("div",null,[s("h1",{class:"text-2xl font-bold text-gray-900"},"Add System User"),s("p",{class:"text-gray-600 mt-1"},"Create a new system user account with role and permissions")],-1)),s("button",{onClick:D,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[Y(V(J),{class:"w-4 h-4 mr-2"}),e[11]||(e[11]=w(" Back to Users "))])])]),I.value?(n(),r("div",Z,e[13]||(e[13]=[s("div",{class:"flex items-center justify-center py-12"},[s("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),s("span",{class:"ml-3 text-gray-600"},"Loading...")],-1)]))):(n(),r("div",ee,[s("form",{onSubmit:$(j,["prevent"]),class:"space-y-6"},[V(k).isSuperUser?b("",!0):(n(),r("div",se,[s("label",te,[e[14]||(e[14]=w(" Organisation ")),x.value?(n(),r("span",le,"("+d(x.value)+")",1)):b("",!0)]),s("div",oe,[a(s("input",{"onUpdate:modelValue":e[0]||(e[0]=l=>A.value=l),onClick:M,placeholder:N.value,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",readonly:""},null,8,re),[[u,A.value]]),y.value?(n(),r("div",ne,[(n(!0),r(v,null,f(h.value,l=>(n(),r("div",{key:l.value,onClick:E=>q(l),class:"cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-100"},d(l.text),9,ae))),128))])):b("",!0)])])),s("div",null,[e[18]||(e[18]=s("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Basic Information",-1)),s("div",ie,[s("div",null,[e[15]||(e[15]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"First Name *",-1)),a(s("input",{"onUpdate:modelValue":e[1]||(e[1]=l=>t.value.first_name=l),type:"text",required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter first name"},null,512),[[u,t.value.first_name]])]),s("div",null,[e[16]||(e[16]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Middle Name",-1)),a(s("input",{"onUpdate:modelValue":e[2]||(e[2]=l=>t.value.middle_name=l),type:"text",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter middle name"},null,512),[[u,t.value.middle_name]])]),s("div",null,[e[17]||(e[17]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Last Name *",-1)),a(s("input",{"onUpdate:modelValue":e[3]||(e[3]=l=>t.value.last_name=l),type:"text",required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter last name"},null,512),[[u,t.value.last_name]])])])]),s("div",null,[e[22]||(e[22]=s("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Contact Information",-1)),s("div",de,[s("div",null,[e[19]||(e[19]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Email Address *",-1)),a(s("input",{"onUpdate:modelValue":e[4]||(e[4]=l=>t.value.email_address=l),type:"email",required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter email address"},null,512),[[u,t.value.email_address]])]),s("div",null,[e[21]||(e[21]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Phone Number *",-1)),s("div",ue,[a(s("select",{"onUpdate:modelValue":e[5]||(e[5]=l=>t.value.dial_code=l),class:"block px-3 py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-50"},e[20]||(e[20]=[s("option",{value:"254"},"+254",-1)]),512),[[_,t.value.dial_code]]),a(s("input",{"onUpdate:modelValue":e[6]||(e[6]=l=>t.value.msisdn=l),type:"tel",required:"",class:"block w-full px-3 py-2 border-t border-r border-b border-gray-300 rounded-r-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"712345678"},null,512),[[u,t.value.msisdn]])])])])]),s("div",null,[e[28]||(e[28]=s("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Identity Information",-1)),s("div",me,[s("div",null,[e[24]||(e[24]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Nationality *",-1)),a(s("select",{"onUpdate:modelValue":e[7]||(e[7]=l=>t.value.nationality=l),required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},[e[23]||(e[23]=s("option",{value:""},"Select Country",-1)),(n(),r(v,null,f(R,l=>s("option",{key:l.value,value:l.value},d(l.text),9,ce)),64))],512),[[_,t.value.nationality]])]),s("div",null,[e[26]||(e[26]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"ID Type *",-1)),a(s("select",{"onUpdate:modelValue":e[8]||(e[8]=l=>t.value.identifier_type=l),required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},[e[25]||(e[25]=s("option",{value:""},"Select ID Type",-1)),(n(),r(v,null,f(P,l=>s("option",{key:l.value,value:l.value},d(l.text),9,be)),64))],512),[[_,t.value.identifier_type]])]),s("div",null,[e[27]||(e[27]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"ID Number *",-1)),a(s("input",{"onUpdate:modelValue":e[9]||(e[9]=l=>t.value.national_id=l),type:"text",required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter ID number"},null,512),[[u,t.value.national_id]])])])]),s("div",null,[e[33]||(e[33]=s("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Role & Permissions",-1)),s("div",ve,[s("div",null,[e[30]||(e[30]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Assign Role *",-1)),a(s("select",{"onUpdate:modelValue":e[10]||(e[10]=l=>t.value.role_id=l),onChange:T,required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},[e[29]||(e[29]=s("option",{value:""},"Choose a Role",-1)),(n(!0),r(v,null,f(g.value,l=>(n(),r("option",{key:l.role_id,value:l.role_id},d(l.role_name),9,fe))),128))],544),[[_,t.value.role_id]]),m.value?(n(),r("p",pe,d(m.value.description||"No description available"),1)):b("",!0)]),m.value&&((c=m.value.permissions)==null?void 0:c.length)>0?(n(),r("div",ge,[s("div",null,[e[32]||(e[32]=s("h4",{class:"text-sm font-medium text-gray-900 mb-2"},"Role Permissions (Auto-selected)",-1)),s("div",ye,[s("div",xe,[(n(!0),r(v,null,f(m.value.permissions,l=>(n(),r("div",{key:l.id,class:"flex items-center text-sm text-gray-700"},[e[31]||(e[31]=s("svg",{class:"w-4 h-4 text-green-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[s("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)),w(" "+d(l.name),1)]))),128))])])])])):b("",!0)])]),s("div",we,[s("button",{type:"button",onClick:D,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"}," Cancel "),s("button",{type:"submit",disabled:p.value||!C.value,class:"inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"},[p.value?(n(),r("div",ke)):(n(),r("svg",he,e[34]||(e[34]=[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},null,-1)]))),e[35]||(e[35]=w(" Create User "))],8,_e)])],32)]))])}}});export{De as default};
