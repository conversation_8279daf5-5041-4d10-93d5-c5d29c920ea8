import{d as S,r as g,a as _,p as O,c as d,b as e,f as b,t as u,h as m,C as L,F,m as M,g as x,v as y,D as U,z as A,o as c}from"./index-DOaBqVmr.js";import{o as f}from"./organizationsApi-CM42ShLT.js";const V={class:"space-y-6"},E={key:0,class:"p-4 bg-green-100 border border-green-400 text-green-700 rounded-md"},j={key:1,class:"p-4 bg-red-100 border border-red-400 text-red-700 rounded-md"},D={class:"bg-white shadow rounded-lg"},T={class:"p-6"},B={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},I=["value"],N={key:2,class:"space-y-6"},$={class:"bg-white shadow rounded-lg"},q={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},G={class:"flex items-center"},H={class:"flex justify-end"},J=["disabled"],K={class:"bg-white shadow rounded-lg"},P={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Q={class:"flex justify-end"},R=["disabled"],W={class:"bg-white shadow rounded-lg"},X={class:"p-6"},Y={class:"flex items-center justify-between"},Z={class:"text-sm text-gray-500"},ee=["disabled"],oe=S({__name:"OrganizationsConfig",setup(te){const n=g(!1),i=g(""),l=g(""),p=g([]),a=g(null),r=_({max_client_loan:0,can_issue_loans:!1}),v=_({service_fee:0}),h=async()=>{try{const s=await f.getOrganizations({limit:100});s.status===200&&(p.value=s.message.data||[])}catch(s){console.error("Error loading organizations:",s)}},w=()=>{a.value&&(r.max_client_loan=a.value.total_loan_assets||0,r.can_issue_loans=a.value.can_issue_loans==="1",v.service_fee=a.value.service_fee||0)},z=async()=>{if(a.value){n.value=!0,l.value="",i.value="";try{const s=await f.setOrganizationLimits({client_account:a.value.client_account,max_client_loan:r.max_client_loan,can_issue_loans:r.can_issue_loans});s.status===200?(i.value="Loan limits updated successfully!",setTimeout(()=>{i.value=""},5e3)):l.value=s.message||"Failed to update loan limits"}catch(s){l.value=s.message||"An error occurred while updating loan limits"}finally{n.value=!1}}},C=async()=>{if(a.value){n.value=!0,l.value="",i.value="";try{const s=await f.setServiceFee({client_account:a.value.client_account,service_fee:v.service_fee});s.status===200?(i.value="Service fee updated successfully!",setTimeout(()=>{i.value=""},5e3)):l.value=s.message||"Failed to update service fee"}catch(s){l.value=s.message||"An error occurred while updating service fee"}finally{n.value=!1}}},k=async()=>{if(!a.value)return;const s=a.value.client_status===1?0:1;n.value=!0,l.value="",i.value="";try{const t=await f.updateOrganization({client_account:a.value.client_account,client_status:s});t.status===200?(a.value.client_status=s,i.value=`Organization ${s===1?"activated":"deactivated"} successfully!`,setTimeout(()=>{i.value=""},5e3)):l.value=t.message||"Failed to update organization status"}catch(t){l.value=t.message||"An error occurred while updating organization status"}finally{n.value=!1}};return O(()=>{h()}),(s,t)=>(c(),d("div",V,[t[15]||(t[15]=e("div",{class:"flex items-center justify-between"},[e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Organization Configuration"),e("p",{class:"mt-1 text-sm text-gray-500"}," Configure organization settings and limits ")])],-1)),i.value?(c(),d("div",E,u(i.value),1)):b("",!0),l.value?(c(),d("div",j,u(l.value),1)):b("",!0),e("div",D,[t[6]||(t[6]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Select Organization")],-1)),e("div",T,[e("div",B,[e("div",null,[t[5]||(t[5]=e("label",{class:"block text-sm font-medium text-gray-700"},"Organization",-1)),m(e("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>a.value=o),onChange:w,class:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"},[t[4]||(t[4]=e("option",{value:""},"Select an organization",-1)),(c(!0),d(F,null,M(p.value,o=>(c(),d("option",{key:o.client_id,value:o},u(o.client_name)+" ("+u(o.client_account)+") ",9,I))),128))],544),[[L,a.value]])])])])]),a.value?(c(),d("div",N,[e("div",$,[t[9]||(t[9]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Loan Limits Configuration")],-1)),e("form",{onSubmit:x(z,["prevent"]),class:"p-6 space-y-6"},[e("div",q,[e("div",null,[t[7]||(t[7]=e("label",{class:"block text-sm font-medium text-gray-700"},"Maximum Client Loan Amount",-1)),m(e("input",{"onUpdate:modelValue":t[1]||(t[1]=o=>r.max_client_loan=o),type:"number",min:"0",step:"0.01",class:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter maximum loan amount"},null,512),[[y,r.max_client_loan,void 0,{number:!0}]])]),e("div",G,[m(e("input",{id:"can_issue_loans","onUpdate:modelValue":t[2]||(t[2]=o=>r.can_issue_loans=o),type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,512),[[U,r.can_issue_loans]]),t[8]||(t[8]=e("label",{for:"can_issue_loans",class:"ml-2 block text-sm text-gray-900"}," Can Issue Loans ",-1))])]),e("div",H,[e("button",{type:"submit",disabled:n.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},u(n.value?"Updating...":"Update Loan Limits"),9,J)])],32)]),e("div",K,[t[12]||(t[12]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Service Fee Configuration")],-1)),e("form",{onSubmit:x(C,["prevent"]),class:"p-6 space-y-6"},[e("div",P,[e("div",null,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-700"},"Service Fee (%)",-1)),m(e("input",{"onUpdate:modelValue":t[3]||(t[3]=o=>v.service_fee=o),type:"number",min:"0",max:"100",step:"0.001",class:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter service fee percentage"},null,512),[[y,v.service_fee,void 0,{number:!0}]]),t[11]||(t[11]=e("p",{class:"mt-1 text-sm text-gray-500"},"Enter as percentage (e.g., 3.5 for 3.5%)",-1))])]),e("div",Q,[e("button",{type:"submit",disabled:n.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},u(n.value?"Updating...":"Update Service Fee"),9,R)])],32)]),e("div",W,[t[14]||(t[14]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Organization Status")],-1)),e("div",X,[e("div",Y,[e("div",null,[t[13]||(t[13]=e("h4",{class:"text-sm font-medium text-gray-900"},"Current Status",-1)),e("p",Z,u(a.value.client_status===1?"Active":"Inactive"),1)]),e("button",{onClick:k,disabled:n.value,class:A(["px-4 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50",a.value.client_status===1?"text-red-700 bg-red-100 hover:bg-red-200 focus:ring-red-500":"text-green-700 bg-green-100 hover:bg-green-200 focus:ring-green-500"])},u(a.value.client_status===1?"Deactivate":"Activate"),11,ee)])])])])):b("",!0)]))}});export{oe as default};
