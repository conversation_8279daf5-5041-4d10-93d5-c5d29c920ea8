import{d as U,r as E,a as x,c as i,b as l,j as f,k as v,l as h,g as B,h as n,f as p,v as d,t as u,C as b,F as _,m as w,i as g,y as D,n as N,o as a}from"./index-DOaBqVmr.js";import{c as P}from"./clientsApi-DPSRLazl.js";import{r as X}from"./ArrowLeftIcon-DKfvydGS.js";const M={class:"space-y-6"},q={class:"flex items-center justify-between"},A={class:"bg-white shadow-sm rounded-lg border border-gray-200"},F={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},K={key:0,class:"mt-1 text-sm text-red-600"},j={key:0,class:"mt-1 text-sm text-red-600"},z={key:0,class:"mt-1 text-sm text-red-600"},I={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},L={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},O=["value"],R=["value"],H={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},T={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200"},$=["disabled"],Y={key:0,class:"flex items-center"},G={key:1},ee=U({__name:"ClientsAdd",setup(J){const k=N(),c=E(!1),s=x({client_name:"",client_email:"",client_phone:"",client_address:"",can_issue_loans:"1",service_fee:"0.030",currency_code:"KES",open_date:"1",close_date:"31",b2c_paybill:"3037395",c2b_paybill:"4114763",sender_id:"SaloPlus"}),r=x({}),C=()=>{var e,m,t;Object.keys(r).forEach(V=>delete r[V]);let o=!0;return(e=s.client_name)!=null&&e.trim()||(r.client_name="Client name is required",o=!1),(m=s.client_email)!=null&&m.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s.client_email)||(r.client_email="Please enter a valid email address",o=!1):(r.client_email="Email address is required",o=!1),(t=s.client_phone)!=null&&t.trim()?/^254\d{9}$/.test(s.client_phone.replace(/\s+/g,""))||(r.client_phone="Please enter a valid Kenyan phone number (254XXXXXXXXX)",o=!1):(r.client_phone="Phone number is required",o=!1),o},S=async()=>{if(C()){c.value=!0;try{const o=await P.addClient(s);o.status===200?k.push({name:"clients"}):console.error("Failed to create client:",o.message)}catch(o){console.error("Error creating client:",o)}finally{c.value=!1}}},y=o=>{const e=parseInt(o);if(e>=11&&e<=13)return"th";switch(e%10){case 1:return"st";case 2:return"nd";case 3:return"rd";default:return"th"}};return(o,e)=>{const m=h("router-link");return a(),i("div",M,[l("div",q,[e[13]||(e[13]=l("div",null,[l("h1",{class:"text-2xl font-bold text-gray-900"},"Add New Client"),l("p",{class:"mt-1 text-sm text-gray-500"}," Create a new client organization ")],-1)),f(m,{to:{name:"clients"},class:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},{default:v(()=>[f(D(X),{class:"h-4 w-4 mr-2"}),e[12]||(e[12]=g(" Back to Clients "))]),_:1,__:[12]})]),l("div",A,[l("form",{onSubmit:B(S,["prevent"]),class:"space-y-6 p-6"},[l("div",null,[e[18]||(e[18]=l("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Basic Information",-1)),l("div",F,[l("div",null,[e[14]||(e[14]=l("label",{for:"client_name",class:"block text-sm font-medium text-gray-700"}," Client Name * ",-1)),n(l("input",{id:"client_name","onUpdate:modelValue":e[0]||(e[0]=t=>s.client_name=t),type:"text",required:"",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter client organization name"},null,512),[[d,s.client_name]]),r.client_name?(a(),i("p",K,u(r.client_name),1)):p("",!0)]),l("div",null,[e[15]||(e[15]=l("label",{for:"client_email",class:"block text-sm font-medium text-gray-700"}," Email Address * ",-1)),n(l("input",{id:"client_email","onUpdate:modelValue":e[1]||(e[1]=t=>s.client_email=t),type:"email",required:"",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter email address"},null,512),[[d,s.client_email]]),r.client_email?(a(),i("p",j,u(r.client_email),1)):p("",!0)]),l("div",null,[e[16]||(e[16]=l("label",{for:"client_phone",class:"block text-sm font-medium text-gray-700"}," Phone Number * ",-1)),n(l("input",{id:"client_phone","onUpdate:modelValue":e[2]||(e[2]=t=>s.client_phone=t),type:"tel",required:"",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"254712345678"},null,512),[[d,s.client_phone]]),r.client_phone?(a(),i("p",z,u(r.client_phone),1)):p("",!0)]),l("div",null,[e[17]||(e[17]=l("label",{for:"client_address",class:"block text-sm font-medium text-gray-700"}," Address ",-1)),n(l("input",{id:"client_address","onUpdate:modelValue":e[3]||(e[3]=t=>s.client_address=t),type:"text",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter physical address"},null,512),[[d,s.client_address]])])])]),l("div",null,[e[25]||(e[25]=l("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Loan Configuration",-1)),l("div",I,[l("div",null,[e[20]||(e[20]=l("label",{for:"can_issue_loans",class:"block text-sm font-medium text-gray-700"}," Can Issue Loans ",-1)),n(l("select",{id:"can_issue_loans","onUpdate:modelValue":e[4]||(e[4]=t=>s.can_issue_loans=t),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},e[19]||(e[19]=[l("option",{value:"1"},"Yes",-1),l("option",{value:"0"},"No",-1)]),512),[[b,s.can_issue_loans]])]),l("div",null,[e[21]||(e[21]=l("label",{for:"service_fee",class:"block text-sm font-medium text-gray-700"}," Service Fee (%) ",-1)),n(l("input",{id:"service_fee","onUpdate:modelValue":e[5]||(e[5]=t=>s.service_fee=t),type:"number",step:"0.001",min:"0",max:"1",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"0.030"},null,512),[[d,s.service_fee]]),e[22]||(e[22]=l("p",{class:"mt-1 text-xs text-gray-500"},"Enter as decimal (e.g., 0.030 for 3%)",-1))]),l("div",null,[e[24]||(e[24]=l("label",{for:"currency_code",class:"block text-sm font-medium text-gray-700"}," Currency ",-1)),n(l("select",{id:"currency_code","onUpdate:modelValue":e[6]||(e[6]=t=>s.currency_code=t),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},e[23]||(e[23]=[l("option",{value:"KES"},"KES - Kenyan Shilling",-1),l("option",{value:"USD"},"USD - US Dollar",-1),l("option",{value:"EUR"},"EUR - Euro",-1)]),512),[[b,s.currency_code]])])])]),l("div",null,[e[28]||(e[28]=l("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Operating Hours",-1)),l("div",L,[l("div",null,[e[26]||(e[26]=l("label",{for:"open_date",class:"block text-sm font-medium text-gray-700"}," Opening Day of Month ",-1)),n(l("select",{id:"open_date","onUpdate:modelValue":e[7]||(e[7]=t=>s.open_date=t),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},[(a(),i(_,null,w(31,t=>l("option",{key:t,value:t.toString()},u(t)+u(y(t.toString())),9,O)),64))],512),[[b,s.open_date]])]),l("div",null,[e[27]||(e[27]=l("label",{for:"close_date",class:"block text-sm font-medium text-gray-700"}," Closing Day of Month ",-1)),n(l("select",{id:"close_date","onUpdate:modelValue":e[8]||(e[8]=t=>s.close_date=t),class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"},[(a(),i(_,null,w(31,t=>l("option",{key:t,value:t.toString()},u(t)+u(y(t.toString())),9,R)),64))],512),[[b,s.close_date]])])])]),l("div",null,[e[33]||(e[33]=l("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Payment Configuration",-1)),l("div",H,[l("div",null,[e[29]||(e[29]=l("label",{for:"b2c_paybill",class:"block text-sm font-medium text-gray-700"}," B2C PayBill ",-1)),n(l("input",{id:"b2c_paybill","onUpdate:modelValue":e[9]||(e[9]=t=>s.b2c_paybill=t),type:"text",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"3037395"},null,512),[[d,s.b2c_paybill]])]),l("div",null,[e[30]||(e[30]=l("label",{for:"c2b_paybill",class:"block text-sm font-medium text-gray-700"}," C2B PayBill ",-1)),n(l("input",{id:"c2b_paybill","onUpdate:modelValue":e[10]||(e[10]=t=>s.c2b_paybill=t),type:"text",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"4114763"},null,512),[[d,s.c2b_paybill]])]),l("div",null,[e[31]||(e[31]=l("label",{for:"sender_id",class:"block text-sm font-medium text-gray-700"}," SMS Sender ID ",-1)),n(l("input",{id:"sender_id","onUpdate:modelValue":e[11]||(e[11]=t=>s.sender_id=t),type:"text",maxlength:"11",class:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500",placeholder:"SaloPlus"},null,512),[[d,s.sender_id]]),e[32]||(e[32]=l("p",{class:"mt-1 text-xs text-gray-500"},"Maximum 11 characters",-1))])])]),l("div",T,[f(m,{to:{name:"clients"},class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},{default:v(()=>e[34]||(e[34]=[g(" Cancel ")])),_:1,__:[34]}),l("button",{type:"submit",disabled:c.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"},[c.value?(a(),i("span",Y,e[35]||(e[35]=[l("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},[l("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),l("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),g(" Creating... ")]))):(a(),i("span",G,"Create Client"))],8,$)])],32)])])}}});export{ee as default};
