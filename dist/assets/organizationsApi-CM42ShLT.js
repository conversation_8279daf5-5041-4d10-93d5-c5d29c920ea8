import{K as d,G as i}from"./index-DOaBqVmr.js";const p={async getOrganizations(a={}){var n,c;try{const r={limit:a.limit||10,offset:a.offset||0,page:a.page||1,status:"1",...a},e=d(r),t=(await i.post("merchant/v1/view/companies",r,{headers:{"X-Hash-Key":e}})).data.data;return{status:t.code===200?200:t.code,message:{data:t.data||[],total_count:t.total_count||0,current_page:t.current_page||1,per_page:t.per_page||r.limit},code:t.code.toString()}}catch(r){if(console.error("Error fetching organizations:",r),r.response){const e=r.response.status,s=r.response.data;return{status:((n=s.data)==null?void 0:n.code)||e,message:{data:[],total_count:0,current_page:1,per_page:a.limit||10},code:(((c=s.data)==null?void 0:c.code)||e).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:a.limit||10},code:"500"}}},async addOrganization(a){var n,c,r;try{const e={company_name:a.client_name,company_phone:a.client_phone,company_email:a.client_email,company_address:a.client_address,contact_person:a.client_name,dial_code:"254"},s=d(e),o=(await i.post("merchant/v1/create_account",e,{headers:{"X-Hash-Key":s}})).data.data;return{status:o.code,message:o.message||o,code:o.code.toString()}}catch(e){if(console.error("Error adding organization:",e),e.response){const s=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||s,message:((c=t.data)==null?void 0:c.message)||"Failed to add organization",code:(((r=t.data)==null?void 0:r.code)||s).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async updateOrganization(a){var n,c,r;try{const e={client_account:a.client_account,status:a.client_status},s=d(e),o=(await i.post("merchant/v1/activate",e,{headers:{"X-Hash-Key":s}})).data.data;return{status:o.code,message:o.message||o,code:o.code.toString()}}catch(e){if(console.error("Error updating organization:",e),e.response){const s=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||s,message:((c=t.data)==null?void 0:c.message)||"Failed to update organization",code:(((r=t.data)==null?void 0:r.code)||s).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async setOrganizationLimits(a){var n,c,r;try{const e={client_account:a.client_account,max_client_loan:a.max_client_loan,can_issue_loans:a.can_issue_loans},s=d(e),o=(await i.post("merchant/v1/set_limits",e,{headers:{"X-Hash-Key":s}})).data.data;return{status:o.code,message:o.message||o,code:o.code.toString()}}catch(e){if(console.error("Error setting organization limits:",e),e.response){const s=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||s,message:((c=t.data)==null?void 0:c.message)||"Failed to set organization limits",code:(((r=t.data)==null?void 0:r.code)||s).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async setServiceFee(a){var n,c,r;try{const e={client_account:a.client_account,service_fee:a.service_fee},s=d(e),o=(await i.post("merchant/v1/set/service_fee",e,{headers:{"X-Hash-Key":s}})).data.data;return{status:o.code,message:o.message||o,code:o.code.toString()}}catch(e){if(console.error("Error setting service fee:",e),e.response){const s=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||s,message:((c=t.data)==null?void 0:c.message)||"Failed to set service fee",code:(((r=t.data)==null?void 0:r.code)||s).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async getOrganizationDetails(a){var n,c,r;try{const e={client_account:a},s=d(e),o=(await i.post("merchant/v1/view/company_details",e,{headers:{"X-Hash-Key":s}})).data.data;return{status:o.code,message:o.data||o,code:o.code.toString()}}catch(e){if(console.error("Error fetching organization details:",e),e.response){const s=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||s,message:((c=t.data)==null?void 0:c.message)||"Failed to fetch organization details",code:(((r=t.data)==null?void 0:r.code)||s).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}}};export{p as o};
