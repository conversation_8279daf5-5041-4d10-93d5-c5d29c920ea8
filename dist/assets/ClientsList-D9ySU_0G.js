import{c as a,b as t,o as i,d as Ie,r as c,a as ee,x as B,p as Ee,A as Ue,f as m,j as u,t as n,i as C,y as h,k as x,l as Ve,T as V,h as f,v as _,C as $,z as U,g as te,n as Ne}from"./index-DOaBqVmr.js";import{_ as De}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{c as R}from"./clientsApi-DPSRLazl.js";import{r as se}from"./MagnifyingGlassIcon-D7MUVaIi.js";import{r as q}from"./BuildingOfficeIcon-Cd0d7gpT.js";import{r as Fe}from"./PlusIcon-BKTWa0k6.js";import{r as le}from"./XMarkIcon-CqcEu60T.js";import{r as $e}from"./EllipsisVerticalIcon-BCCOPEML.js";import{r as je,a as Pe}from"./PencilIcon-Bgul0WAp.js";function Te(oe,K){return i(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"})])}const Le={class:"space-y-6"},Me={key:0,class:"p-4 bg-green-100 border border-green-400 text-green-700 rounded-md"},Oe={key:1,class:"p-4 bg-red-100 border border-red-400 text-red-700 rounded-md"},ze={class:"flex items-center justify-between"},Be={class:"flex items-center space-x-3"},Re={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},qe={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},Ke={class:"flex items-center"},He={class:"flex-shrink-0"},We={class:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center"},Je={class:"ml-4"},Qe={class:"text-2xl font-bold text-gray-900"},Ye={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},Ze={class:"flex items-center"},Ge={class:"flex-shrink-0"},Xe={class:"w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center"},et={class:"ml-4"},tt={class:"text-2xl font-bold text-gray-900"},st={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},lt={class:"flex items-center"},ot={class:"flex-shrink-0"},nt={class:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center"},rt={class:"ml-4"},at={class:"text-2xl font-bold text-gray-900"},it={key:0,class:"bg-white rounded-xl shadow-sm border border-gray-200 p-4"},ut={class:"flex items-center space-x-4"},dt={class:"flex-1"},ct={class:"relative"},mt={key:0,class:"bg-white rounded-xl shadow-sm border border-gray-200"},vt={class:"px-6 py-4 border-b border-gray-200"},ft={class:"flex items-center justify-between"},bt={class:"p-6"},pt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},gt={class:"font-medium text-gray-900"},yt={class:"text-sm"},xt={class:"font-medium text-gray-900"},_t={class:"text-gray-500"},ht={class:"text-sm"},wt={class:"font-medium text-gray-900"},kt={class:"text-gray-500"},Ct={class:"text-sm text-gray-500"},At={class:"relative"},St=["onClick"],It={key:0,class:"absolute right-0 z-10 mt-2 w-56 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5"},Et={class:"py-1"},Ut=["onClick"],Vt=["onClick"],Nt={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Dt={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},Ft={class:"mt-3"},$t={key:0,class:"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded"},jt={key:1,class:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded"},Pt={class:"space-y-4"},Tt={key:0,class:"mt-1 text-sm text-red-600"},Lt={key:0,class:"mt-1 text-sm text-red-600"},Mt={key:0,class:"mt-1 text-sm text-red-600"},Ot={class:"flex justify-end space-x-3 mt-6"},zt=["disabled"],Bt={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Rt={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},qt={class:"mt-3"},Kt={key:0,class:"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded"},Ht={key:1,class:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded"},Wt={class:"space-y-4"},Jt={key:0,class:"mt-1 text-sm text-red-600"},Qt={key:0,class:"mt-1 text-sm text-red-600"},Yt={key:0,class:"mt-1 text-sm text-red-600"},Zt={class:"flex justify-end space-x-3 mt-6"},Gt=["disabled"],is=Ie({__name:"ClientsList",setup(oe){const K=Ne(),w=c(!1),b=c([]),S=c(1),N=c(0),j=c(10),I=c(""),H=c(""),W=c("asc"),P=c(!1),D=c(!1),k=ee({}),T=c(!1),L=c(!1),y=ee({status:"",account:"",clientName:"",contactInfo:"",currency:"",canIssueLoans:"",serviceFeeMin:"",serviceFeeMax:"",dateFrom:"",dateTo:""}),v=c({client_name:"",client_phone:"",client_email:"",client_address:""}),M=c(null),r=c({client_id:"",client_account:"",client_name:"",client_phone:"",client_email:"",client_address:"",client_status:"1"}),o=c({}),p=c(""),g=c(""),ne={client_account:"Account",client_name:"Client Name",client_phone:"Contact Info",total_loan_assets:"Loan Assets",open_date:"Operating Hours",b2c_paybill:"PayBill Numbers",client_status:"Status",created:"Created Date"},re=B(()=>Array.isArray(b.value)?b.value.filter(s=>s.client_status==="1").length:0),ae=B(()=>Array.isArray(b.value)?b.value.filter(s=>s.client_status==="0").length:0),ie=B(()=>Array.isArray(b.value)?b.value.filter(s=>s.can_issue_loans==="1").length:0),A=async(s={})=>{w.value=!0;try{const e={page:S.value,limit:j.value,offset:(S.value-1)*j.value,search:I.value,sortField:H.value,sortDirection:W.value,...s},d=await R.getClients(e);if(console.log("response",JSON.stringify(d)),d.status===200){const l=d.message;b.value=Array.isArray(l.data)?l.data:[],N.value=parseInt(l.total_count.toString())||0,S.value=l.current_page||1,localStorage.setItem("clients",JSON.stringify(b.value))}else console.error("Failed to fetch clients:",d.message),b.value=[],N.value=0}catch(e){console.error("Error fetching clients:",e),b.value=[],N.value=0}finally{w.value=!1}},ue=s=>{S.value=s,A()},O=s=>{I.value=s,S.value=1,A()},de=(s,e)=>{H.value=s,W.value=e,S.value=1,A()},ce=s=>{console.log("Row clicked:",s)},me=()=>{A()},ve=s=>{Object.keys(k).forEach(e=>{parseInt(e)!==s&&(k[parseInt(e)]=!1)}),k[s]=!k[s]},fe=()=>{var s,e,d;return o.value={},(s=v.value.client_name)!=null&&s.trim()||(o.value.client_name="Client name is required"),(e=v.value.client_phone)!=null&&e.trim()?/^[0-9+\-\s()]+$/.test(v.value.client_phone)||(o.value.client_phone="Please enter a valid phone number"):o.value.client_phone="Phone number is required",(d=v.value.client_email)!=null&&d.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v.value.client_email)||(o.value.client_email="Please enter a valid email address"):o.value.client_email="Email is required",Object.keys(o.value).length===0},be=()=>{var s,e,d;return o.value={},(s=r.value.client_name)!=null&&s.trim()||(o.value.client_name="Client name is required"),(e=r.value.client_phone)!=null&&e.trim()?/^[0-9+\-\s()]+$/.test(r.value.client_phone)||(o.value.client_phone="Please enter a valid phone number"):o.value.client_phone="Phone number is required",(d=r.value.client_email)!=null&&d.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r.value.client_email)||(o.value.client_email="Please enter a valid email address"):o.value.client_email="Email is required",Object.keys(o.value).length===0},pe=async()=>{if(g.value="",p.value="",!!fe()){w.value=!0;try{const s={...v.value,client_phone:Z(v.value.client_phone||"")},e=await R.addClient(s);e.status===200?(p.value="Client added successfully!",P.value=!1,v.value={client_name:"",client_phone:"",client_email:"",client_address:""},o.value={},A(),setTimeout(()=>{p.value=""},5e3)):g.value=e.message||"Failed to add client"}catch(s){g.value=s.message||"An error occurred while adding the client"}finally{w.value=!1}}},ge=s=>{M.value=s,r.value={client_id:s.client_id,client_account:s.client_account,client_name:s.client_name,client_phone:s.client_phone,client_email:s.client_email,client_address:s.client_address||"",client_status:s.client_status},o.value={},g.value="",p.value="",D.value=!0,k[b.value.indexOf(s)]=!1},ye=async()=>{if(g.value="",p.value="",!!be()){w.value=!0;try{const s={...r.value,client_phone:Z(r.value.client_phone||"")},e=await R.updateClient(s);if(e.status===200){p.value="Client updated successfully!",D.value=!1;const d=b.value.findIndex(l=>l.client_id===r.value.client_id);d!==-1&&(b.value[d]={...b.value[d],...s}),o.value={},M.value=null,setTimeout(()=>{p.value=""},5e3)}else g.value=e.message||"Failed to update client"}catch(s){g.value=s.message||"An error occurred while updating the client"}finally{w.value=!1}}},xe=()=>{D.value=!1,M.value=null,o.value={},g.value="",p.value=""},_e=()=>{P.value=!1,v.value={client_name:"",client_phone:"",client_email:"",client_address:""},o.value={},g.value="",p.value=""},he=s=>{console.log("View details for:",s.client_name),k[b.value.indexOf(s)]=!1,K.push({name:"clients-view",params:{id:s.client_id}})};let J;const we=()=>{clearTimeout(J),J=setTimeout(()=>{O(I.value)},300)},ke=()=>{I.value="",O("")};let Q;const z=()=>{clearTimeout(Q),Q=setTimeout(()=>{F()},300)},F=()=>{A()},Ce=()=>{Object.keys(y).forEach(s=>{y[s]=""}),A()},Ae=s=>{const e=typeof s=="string"?parseFloat(s):s;return new Intl.NumberFormat("en-KE",{style:"currency",currency:"KES"}).format(e||0)},Se=s=>s?new Date(s).toLocaleDateString("en-KE",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"N/A",Y=s=>{const e=parseInt(s);if(e>=11&&e<=13)return"th";switch(e%10){case 1:return"st";case 2:return"nd";case 3:return"rd";default:return"th"}},Z=s=>{const e=s.replace(/\D/g,"");return e.length===9&&e.startsWith("7")?`254${e}`:e.length===10&&e.startsWith("07")?`254${e.substring(1)}`:e.length===12&&e.startsWith("254")?e:s},G=s=>{s.target.closest(".relative")||Object.keys(k).forEach(d=>{k[parseInt(d)]=!1})};return Ee(()=>{A(),document.addEventListener("click",G)}),Ue(()=>{document.removeEventListener("click",G)}),(s,e)=>{const d=Ve("router-link");return i(),a("div",Le,[p.value?(i(),a("div",Me,n(p.value),1)):m("",!0),g.value?(i(),a("div",Oe,n(g.value),1)):m("",!0),t("div",ze,[e[22]||(e[22]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900"},"Clients"),t("p",{class:"mt-1 text-sm text-gray-500"}," Manage and view all registered clients ")],-1)),t("div",Be,[t("button",{onClick:e[0]||(e[0]=l=>T.value=!T.value),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[u(h(se),{class:"h-4 w-4 mr-2"}),e[19]||(e[19]=C(" Search "))]),t("button",{onClick:e[1]||(e[1]=l=>L.value=!L.value),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[u(h(Te),{class:"h-4 w-4 mr-2"}),e[20]||(e[20]=C(" Filters "))]),u(d,{to:{name:"clients-add"},class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},{default:x(()=>[u(h(Fe),{class:"h-4 w-4 mr-2"}),e[21]||(e[21]=C(" Add Client "))]),_:1,__:[21]})])]),t("div",Re,[t("div",qe,[t("div",Ke,[t("div",He,[t("div",We,[u(h(q),{class:"w-5 h-5 text-green-600"})])]),t("div",Je,[e[23]||(e[23]=t("p",{class:"text-sm font-medium text-gray-500"},"Active Clients",-1)),t("p",Qe,n(re.value),1)])])]),t("div",Ye,[t("div",Ze,[t("div",Ge,[t("div",Xe,[u(h(q),{class:"w-5 h-5 text-red-600"})])]),t("div",et,[e[24]||(e[24]=t("p",{class:"text-sm font-medium text-gray-500"},"Inactive Clients",-1)),t("p",tt,n(ae.value),1)])])]),t("div",st,[t("div",lt,[t("div",ot,[t("div",nt,[u(h(q),{class:"w-5 h-5 text-blue-600"})])]),t("div",rt,[e[25]||(e[25]=t("p",{class:"text-sm font-medium text-gray-500"},"Can Issue Loans",-1)),t("p",at,n(ie.value),1)])])])]),u(V,{"enter-active-class":"transition ease-out duration-200","enter-from-class":"opacity-0 -translate-y-2","enter-to-class":"opacity-100 translate-y-0","leave-active-class":"transition ease-in duration-150","leave-from-class":"opacity-100 translate-y-0","leave-to-class":"opacity-0 -translate-y-2"},{default:x(()=>[T.value?(i(),a("div",it,[t("div",ut,[t("div",dt,[t("div",ct,[u(h(se),{class:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),f(t("input",{"onUpdate:modelValue":e[2]||(e[2]=l=>I.value=l),type:"text",placeholder:"Search clients by name, account, email, or phone...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onInput:we},null,544),[[_,I.value]])])]),I.value?(i(),a("button",{key:0,onClick:ke,class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},[u(h(le),{class:"h-4 w-4 mr-2"}),e[26]||(e[26]=C(" Clear "))])):m("",!0)])])):m("",!0)]),_:1}),u(V,{"enter-active-class":"transition ease-out duration-200","enter-from-class":"opacity-0 -translate-y-2","enter-to-class":"opacity-100 translate-y-0","leave-active-class":"transition ease-in duration-150","leave-from-class":"opacity-100 translate-y-0","leave-to-class":"opacity-0 -translate-y-2"},{default:x(()=>[L.value?(i(),a("div",mt,[t("div",vt,[t("div",ft,[e[28]||(e[28]=t("h3",{class:"text-lg font-medium text-gray-900"},"Filters",-1)),t("button",{onClick:Ce,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[u(h(le),{class:"h-4 w-4 mr-2"}),e[27]||(e[27]=C(" Clear All "))])])]),t("div",bt,[t("div",pt,[t("div",null,[e[30]||(e[30]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Status",-1)),f(t("select",{"onUpdate:modelValue":e[3]||(e[3]=l=>y.status=l),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",onChange:F},e[29]||(e[29]=[t("option",{value:""},"All Statuses",-1),t("option",{value:"1"},"Active",-1),t("option",{value:"0"},"Inactive",-1)]),544),[[$,y.status]])]),t("div",null,[e[31]||(e[31]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Account",-1)),f(t("input",{"onUpdate:modelValue":e[4]||(e[4]=l=>y.account=l),type:"text",placeholder:"Search by account...",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",onInput:z},null,544),[[_,y.account]])]),t("div",null,[e[32]||(e[32]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Client Name",-1)),f(t("input",{"onUpdate:modelValue":e[5]||(e[5]=l=>y.clientName=l),type:"text",placeholder:"Search by name...",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",onInput:z},null,544),[[_,y.clientName]])]),t("div",null,[e[33]||(e[33]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Contact Info",-1)),f(t("input",{"onUpdate:modelValue":e[6]||(e[6]=l=>y.contactInfo=l),type:"text",placeholder:"Email or phone...",class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",onInput:z},null,544),[[_,y.contactInfo]])]),t("div",null,[e[35]||(e[35]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Currency",-1)),f(t("select",{"onUpdate:modelValue":e[7]||(e[7]=l=>y.currency=l),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",onChange:F},e[34]||(e[34]=[t("option",{value:""},"All Currencies",-1),t("option",{value:"KES"},"KES",-1),t("option",{value:"USD"},"USD",-1),t("option",{value:"EUR"},"EUR",-1)]),544),[[$,y.currency]])]),t("div",null,[e[37]||(e[37]=t("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Can Issue Loans",-1)),f(t("select",{"onUpdate:modelValue":e[8]||(e[8]=l=>y.canIssueLoans=l),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",onChange:F},e[36]||(e[36]=[t("option",{value:""},"All",-1),t("option",{value:"1"},"Yes",-1),t("option",{value:"0"},"No",-1)]),544),[[$,y.canIssueLoans]])])])])])):m("",!0)]),_:1}),u(De,{data:b.value,headers:ne,loading:w.value,"current-page":S.value,"total-records":N.value,"page-size":j.value,title:"Clients","row-key":"client_id","has-actions":!0,onPageChange:ue,onSearch:O,onSort:de,onRowClick:ce},{"header-actions":x(()=>[t("button",{onClick:me,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},e[38]||(e[38]=[t("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),C(" Refresh ")]))]),"cell-client_status":x(({value:l})=>[t("span",{class:U(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",{"bg-green-100 text-green-800":l==="1","bg-red-100 text-red-800":l==="0"}])},n(l==="1"?"Active":"Inactive"),3)]),"cell-total_loan_assets":x(({value:l})=>[t("span",gt,n(Ae(l)),1)]),"cell-open_date":x(({value:l,item:E})=>[t("div",yt,[t("div",xt,n(l)+n(Y(l)),1),t("div",_t,"to "+n(E.close_date)+n(Y(E.close_date)),1)])]),"cell-b2c_paybill":x(({value:l,item:E})=>[t("div",ht,[t("div",wt,"B2C: "+n(l),1),t("div",kt,"C2B: "+n(E.c2b_paybill),1)])]),"cell-created":x(({value:l})=>[t("span",Ct,n(Se(l)),1)]),actions:x(({item:l,index:E})=>[t("div",At,[t("button",{onClick:X=>ve(E),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[e[39]||(e[39]=C(" Actions ")),u(h($e),{class:"h-4 w-4 ml-2"})],8,St),u(V,{"enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:x(()=>[k[E]?(i(),a("div",It,[t("div",Et,[t("button",{onClick:X=>ge(l),class:"flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"},[u(h(je),{class:"h-4 w-4 mr-3"}),e[40]||(e[40]=C(" Edit Client "))],8,Ut),t("button",{onClick:X=>he(l),class:"flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"},[u(h(Pe),{class:"h-4 w-4 mr-3"}),e[41]||(e[41]=C(" View Details "))],8,Vt)])])):m("",!0)]),_:2},1024)])]),_:1},8,["data","loading","current-page","total-records","page-size"]),u(V,{"enter-active-class":"transition ease-out duration-300","enter-from-class":"opacity-0","enter-to-class":"opacity-100","leave-active-class":"transition ease-in duration-200","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:x(()=>[P.value?(i(),a("div",Nt,[t("div",Dt,[t("div",Ft,[e[46]||(e[46]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Add New Client",-1)),p.value?(i(),a("div",$t,n(p.value),1)):m("",!0),g.value?(i(),a("div",jt,n(g.value),1)):m("",!0),t("form",{onSubmit:te(pe,["prevent"])},[t("div",Pt,[t("div",null,[e[42]||(e[42]=t("label",{class:"block text-sm font-medium text-gray-700"},"Client Name *",-1)),f(t("input",{"onUpdate:modelValue":e[9]||(e[9]=l=>v.value.client_name=l),type:"text",class:U(["mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",o.value.client_name?"border-red-300":"border-gray-300"])},null,2),[[_,v.value.client_name]]),o.value.client_name?(i(),a("p",Tt,n(o.value.client_name),1)):m("",!0)]),t("div",null,[e[43]||(e[43]=t("label",{class:"block text-sm font-medium text-gray-700"},"Phone Number *",-1)),f(t("input",{"onUpdate:modelValue":e[10]||(e[10]=l=>v.value.client_phone=l),type:"tel",class:U(["mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",o.value.client_phone?"border-red-300":"border-gray-300"])},null,2),[[_,v.value.client_phone]]),o.value.client_phone?(i(),a("p",Lt,n(o.value.client_phone),1)):m("",!0)]),t("div",null,[e[44]||(e[44]=t("label",{class:"block text-sm font-medium text-gray-700"},"Email Address *",-1)),f(t("input",{"onUpdate:modelValue":e[11]||(e[11]=l=>v.value.client_email=l),type:"email",class:U(["mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",o.value.client_email?"border-red-300":"border-gray-300"])},null,2),[[_,v.value.client_email]]),o.value.client_email?(i(),a("p",Mt,n(o.value.client_email),1)):m("",!0)]),t("div",null,[e[45]||(e[45]=t("label",{class:"block text-sm font-medium text-gray-700"},"Address",-1)),f(t("textarea",{"onUpdate:modelValue":e[12]||(e[12]=l=>v.value.client_address=l),rows:"3",class:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,512),[[_,v.value.client_address]])])]),t("div",Ot,[t("button",{type:"button",onClick:_e,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"}," Cancel "),t("button",{type:"submit",disabled:w.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},n(w.value?"Adding...":"Add Client"),9,zt)])],32)])])])):m("",!0)]),_:1}),u(V,{"enter-active-class":"transition ease-out duration-300","enter-from-class":"opacity-0","enter-to-class":"opacity-100","leave-active-class":"transition ease-in duration-200","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:x(()=>[D.value?(i(),a("div",Bt,[t("div",Rt,[t("div",qt,[e[54]||(e[54]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Edit Client",-1)),p.value?(i(),a("div",Kt,n(p.value),1)):m("",!0),g.value?(i(),a("div",Ht,n(g.value),1)):m("",!0),t("form",{onSubmit:te(ye,["prevent"])},[t("div",Wt,[t("div",null,[e[47]||(e[47]=t("label",{class:"block text-sm font-medium text-gray-700"},"Client Account",-1)),f(t("input",{"onUpdate:modelValue":e[13]||(e[13]=l=>r.value.client_account=l),type:"text",disabled:"",class:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 bg-gray-100 text-gray-500"},null,512),[[_,r.value.client_account]])]),t("div",null,[e[48]||(e[48]=t("label",{class:"block text-sm font-medium text-gray-700"},"Client Name *",-1)),f(t("input",{"onUpdate:modelValue":e[14]||(e[14]=l=>r.value.client_name=l),type:"text",class:U(["mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",o.value.client_name?"border-red-300":"border-gray-300"])},null,2),[[_,r.value.client_name]]),o.value.client_name?(i(),a("p",Jt,n(o.value.client_name),1)):m("",!0)]),t("div",null,[e[49]||(e[49]=t("label",{class:"block text-sm font-medium text-gray-700"},"Phone Number *",-1)),f(t("input",{"onUpdate:modelValue":e[15]||(e[15]=l=>r.value.client_phone=l),type:"tel",class:U(["mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",o.value.client_phone?"border-red-300":"border-gray-300"])},null,2),[[_,r.value.client_phone]]),o.value.client_phone?(i(),a("p",Qt,n(o.value.client_phone),1)):m("",!0)]),t("div",null,[e[50]||(e[50]=t("label",{class:"block text-sm font-medium text-gray-700"},"Email Address *",-1)),f(t("input",{"onUpdate:modelValue":e[16]||(e[16]=l=>r.value.client_email=l),type:"email",class:U(["mt-1 block w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500",o.value.client_email?"border-red-300":"border-gray-300"])},null,2),[[_,r.value.client_email]]),o.value.client_email?(i(),a("p",Yt,n(o.value.client_email),1)):m("",!0)]),t("div",null,[e[51]||(e[51]=t("label",{class:"block text-sm font-medium text-gray-700"},"Address",-1)),f(t("textarea",{"onUpdate:modelValue":e[17]||(e[17]=l=>r.value.client_address=l),rows:"3",class:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,512),[[_,r.value.client_address]])]),t("div",null,[e[53]||(e[53]=t("label",{class:"block text-sm font-medium text-gray-700"},"Status",-1)),f(t("select",{"onUpdate:modelValue":e[18]||(e[18]=l=>r.value.client_status=l),class:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"},e[52]||(e[52]=[t("option",{value:"1"},"Active",-1),t("option",{value:"0"},"Inactive",-1)]),512),[[$,r.value.client_status]])])]),t("div",Zt,[t("button",{type:"button",onClick:xe,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"}," Cancel "),t("button",{type:"submit",disabled:w.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},n(w.value?"Updating...":"Update Client"),9,Gt)])],32)])])])):m("",!0)]),_:1})])}}});export{is as default};
