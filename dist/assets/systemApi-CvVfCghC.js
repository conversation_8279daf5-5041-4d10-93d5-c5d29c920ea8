import{K as p,G as u}from"./index-DOaBqVmr.js";const S={async getUsers(s={}){var o,d;try{const a={page:s.page||1,per_page:s.limit||10,role_id:s.role_id||"",status:s.status||"",client_id:s.client_id||"",search:s.search||"",...s},e=p(a),r=new URLSearchParams(Object.entries(a).filter(([i,g])=>g!=="").map(([i,g])=>[i,String(g)])).toString(),t=(await u.get(`merchant/v1/users?${r}`,{headers:{"X-Hash-Key":e}})).data.data;return{status:t.code===200?200:t.code,message:{data:t.data||[],total_count:t.total_count||0,current_page:t.current_page||1,per_page:t.per_page||a.per_page},code:t.code.toString()}}catch(a){if(console.error("Error fetching users:",a),a.response){const e=a.response.status,r=a.response.data;return{status:((o=r.data)==null?void 0:o.code)||e,message:{data:[],total_count:0,current_page:1,per_page:s.limit||10},code:(((d=r.data)==null?void 0:d.code)||e).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:s.limit||10},code:"500"}}},async createUser(s){var o,d,a;try{const e={username:s.username,email_address:s.email_address,msisdn:s.msisdn,role_id:s.role_id,permissions:s.permissions,client_id:s.client_id||"",password:s.password?btoa(s.password):""},r=p(e),t=(await u.post("merchant/v1/user_create",e,{headers:{"X-Hash-Key":r}})).data.data;return{status:t.code,message:t.message||t,code:t.code.toString()}}catch(e){if(console.error("Error creating user:",e),e.response){const r=e.response.status,n=e.response.data;return{status:((o=n.data)==null?void 0:o.code)||r,message:((d=n.data)==null?void 0:d.message)||"Failed to create user",code:(((a=n.data)==null?void 0:a.code)||r).toString()}}return{status:500,message:"Failed to create user",code:"500"}}},async updateUser(s){var o,d,a;try{const e={user_id:s.user_id,username:s.username,email_address:s.email_address,msisdn:s.msisdn,role_id:s.role_id,permissions:s.permissions,status:s.status},r=p(e),t=(await u.post("merchant/v1/user_update",e,{headers:{"X-Hash-Key":r}})).data.data;return{status:t.code,message:t.message||t,code:t.code.toString()}}catch(e){if(console.error("Error updating user:",e),e.response){const r=e.response.status,n=e.response.data;return{status:((o=n.data)==null?void 0:o.code)||r,message:((d=n.data)==null?void 0:d.message)||"Failed to update user",code:(((a=n.data)==null?void 0:a.code)||r).toString()}}return{status:500,message:"Failed to update user",code:"500"}}},async deleteUser(s){var o,d,a;try{const e={user_id:s},r=p(e),t=(await u.post("merchant/v1/user_delete",e,{headers:{"X-Hash-Key":r}})).data.data;return{status:t.code,message:t.message||t,code:t.code.toString()}}catch(e){if(console.error("Error deleting user:",e),e.response){const r=e.response.status,n=e.response.data;return{status:((o=n.data)==null?void 0:o.code)||r,message:((d=n.data)==null?void 0:d.message)||"Failed to delete user",code:(((a=n.data)==null?void 0:a.code)||r).toString()}}return{status:500,message:"Failed to delete user",code:"500"}}},async getRoles(s={}){var o,d,a,e,r,n,t,i,g,h;try{const c={page:s.page||1,per_page:s.limit||10,status:s.status||"",search:s.search||"",...s},m=p(c),l=(await u.post("merchant/v1/user_roles",c,{headers:{"X-Hash-Key":m}})).data.data;return{status:l.code===200?200:l.code,message:{data:((d=(o=l.data)==null?void 0:o.data)==null?void 0:d.data)||[],total_count:((e=(a=l.data)==null?void 0:a.data)==null?void 0:e.total)||0,current_page:((n=(r=l.data)==null?void 0:r.data)==null?void 0:n.current_page)||1,per_page:((i=(t=l.data)==null?void 0:t.data)==null?void 0:i.per_page)||c.per_page},code:l.code.toString()}}catch(c){if(console.error("Error fetching roles:",c),c.response){const m=c.response.status,_=c.response.data;return{status:((g=_.data)==null?void 0:g.code)||m,message:{data:[],total_count:0,current_page:1,per_page:s.limit||10},code:(((h=_.data)==null?void 0:h.code)||m).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:s.limit||10},code:"500"}}},async createRole(s){var o,d,a;try{const e={role_name:s.role_name,description:s.description||"",permissions:s.permissions},r=p(e),t=(await u.post("merchant/v1/role/create",e,{headers:{"X-Hash-Key":r}})).data.data;return{status:t.code,message:t.message||t,code:t.code.toString()}}catch(e){if(console.error("Error creating role:",e),e.response){const r=e.response.status,n=e.response.data;return{status:((o=n.data)==null?void 0:o.code)||r,message:((d=n.data)==null?void 0:d.message)||"Failed to create role",code:(((a=n.data)==null?void 0:a.code)||r).toString()}}return{status:500,message:"Failed to create role",code:"500"}}},async updateRole(s){var o,d,a;try{const e={role_id:s.role_id,role_name:s.role_name,description:s.description,permissions:s.permissions,status:s.status},r=p(e),t=(await u.post("merchant/v1/role/update",e,{headers:{"X-Hash-Key":r}})).data.data;return{status:t.code,message:t.message||t,code:t.code.toString()}}catch(e){if(console.error("Error updating role:",e),e.response){const r=e.response.status,n=e.response.data;return{status:((o=n.data)==null?void 0:o.code)||r,message:((d=n.data)==null?void 0:d.message)||"Failed to update role",code:(((a=n.data)==null?void 0:a.code)||r).toString()}}return{status:500,message:"Failed to update role",code:"500"}}},async deleteRole(s){var o,d,a;try{const e={role_id:s},r=p(e),t=(await u.post("merchant/v1/role/delete",e,{headers:{"X-Hash-Key":r}})).data.data;return{status:t.code,message:t.message||t,code:t.code.toString()}}catch(e){if(console.error("Error deleting role:",e),e.response){const r=e.response.status,n=e.response.data;return{status:((o=n.data)==null?void 0:o.code)||r,message:((d=n.data)==null?void 0:d.message)||"Failed to delete role",code:(((a=n.data)==null?void 0:a.code)||r).toString()}}return{status:500,message:"Failed to delete role",code:"500"}}},async getPermissions(s={}){var o,d,a,e,r,n;try{const t={page:s.page||1,per_page:s.limit||100},i=p(t),g=new URLSearchParams(t).toString(),c=(await u.get(`merchant/v1/permission/view?${g}`,{headers:{"X-Hash-Key":i}})).data.data;return{status:c.code===200?200:c.code,message:{data:((o=c.data)==null?void 0:o.data)||[],total_count:((d=c.data)==null?void 0:d.total)||0,current_page:((a=c.data)==null?void 0:a.current_page)||1,per_page:((e=c.data)==null?void 0:e.per_page)||t.per_page},code:c.code.toString()}}catch(t){if(console.error("Error fetching permissions:",t),t.response){const i=t.response.status,g=t.response.data;return{status:((r=g.data)==null?void 0:r.code)||i,message:{data:[],total_count:0,current_page:1,per_page:s.limit||100},code:(((n=g.data)==null?void 0:n.code)||i).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:s.limit||100},code:"500"}}},async assignClient(s,o){var d,a,e;try{const r={user_id:s,client_id:o},n=p(r),i=(await u.post("merchant/v1/user_assign_client",r,{headers:{"X-Hash-Key":n}})).data.data;return{status:i.code,message:i.message||i,code:i.code.toString()}}catch(r){if(console.error("Error assigning client:",r),r.response){const n=r.response.status,t=r.response.data;return{status:((d=t.data)==null?void 0:d.code)||n,message:((a=t.data)==null?void 0:a.message)||"Failed to assign client",code:(((e=t.data)==null?void 0:e.code)||n).toString()}}return{status:500,message:"Failed to assign client",code:"500"}}},async resendOTP(s){var o,d,a;try{const e={user_id:s},r=p(e),t=(await u.post("merchant/v1/user_resend_otp",e,{headers:{"X-Hash-Key":r}})).data.data;return{status:t.code,message:t.message||t,code:t.code.toString()}}catch(e){if(console.error("Error resending OTP:",e),e.response){const r=e.response.status,n=e.response.data;return{status:((o=n.data)==null?void 0:o.code)||r,message:((d=n.data)==null?void 0:d.message)||"Failed to resend OTP",code:(((a=n.data)==null?void 0:a.code)||r).toString()}}return{status:500,message:"Failed to resend OTP",code:"500"}}},async createPermission(s){try{const o=p(s),a=(await u.post("merchant/v1/permissions",s,{headers:{"X-Hash-Key":o}})).data.data;return{status:a.code,message:a.message||a,code:a.code.toString()}}catch(o){if(console.error("Error creating permission:",o),o.response){const d=o.response.status,a=o.response.data;return{status:d,message:a.message||"Failed to create permission",code:d.toString()}}else return{status:500,message:"Network error occurred",code:"500"}}},async updatePermission(s){try{const o=p(s),a=(await u.put(`merchant/v1/permissions/${s.id}`,s,{headers:{"X-Hash-Key":o}})).data.data;return{status:a.code,message:a.message||a,code:a.code.toString()}}catch(o){if(console.error("Error updating permission:",o),o.response){const d=o.response.status,a=o.response.data;return{status:d,message:a.message||"Failed to update permission",code:d.toString()}}else return{status:500,message:"Network error occurred",code:"500"}}},async deletePermission(s){try{const d=p({id:s}),e=(await u.delete(`merchant/v1/permissions/${s}`,{headers:{"X-Hash-Key":d}})).data.data;return{status:e.code,message:e.message||e,code:e.code.toString()}}catch(o){if(console.error("Error deleting permission:",o),o.response){const d=o.response.status,a=o.response.data;return{status:d,message:a.message||"Failed to delete permission",code:d.toString()}}else return{status:500,message:"Network error occurred",code:"500"}}}};export{S as s};
