import{d as R,r as a,a as U,p as $,c as u,b as e,j as N,i as x,t as v,h as _,C as h,e as q,F as G,m as Q,k as f,z as J,n as K,o as p}from"./index-DOaBqVmr.js";import{_ as O}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";const W={class:"space-y-6"},X={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},Y={class:"flex items-center justify-between"},ee={class:"flex space-x-3"},te=["disabled"],se={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},oe={key:1,class:"-ml-1 mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ne={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},re={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ae=["value"],ie={class:"flex space-x-2"},le={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},de={class:"flex items-center space-x-2"},ce=["onClick"],ue=["onClick"],pe=["onClick"],ge=R({__name:"PartnerServices",setup(me){K();const i=a(!1),w=a([]),k=a([]),d=a(1),C=a(0),g=a(10),b=a(""),m=a(""),S=a("asc"),B=a(!1),r=U({status:"",service_type:"",partner_id:""}),D=[{id:1,service_name:"Betting API",service_type:"betting",partner_name:"SportsBet Pro",partner_id:1,status:"active",endpoint_url:"https://api.sportsbet.com/v1",api_key:"sb_***************",created_at:"2024-01-15T10:30:00Z",updated_at:"2024-01-20T14:45:00Z",last_sync:"2024-01-31T09:15:00Z"},{id:2,service_name:"Payment Gateway",service_type:"payment",partner_name:"PayFast Solutions",partner_id:2,status:"active",endpoint_url:"https://api.payfast.co.za/v1",api_key:"pf_***************",created_at:"2024-01-10T08:20:00Z",updated_at:"2024-01-25T16:30:00Z",last_sync:"2024-01-31T08:45:00Z"},{id:3,service_name:"Analytics Service",service_type:"analytics",partner_name:"DataInsights Ltd",partner_id:3,status:"pending",endpoint_url:"https://api.datainsights.com/v2",api_key:"di_***************",created_at:"2024-01-28T12:00:00Z",updated_at:"2024-01-30T10:15:00Z",last_sync:null}],M=[{id:1,name:"SportsBet Pro"},{id:2,name:"PayFast Solutions"},{id:3,name:"DataInsights Ltd"}],l=async()=>{i.value=!0;try{await new Promise(n=>setTimeout(n,1e3));let s=[...D];if(r.status&&(s=s.filter(n=>n.status===r.status)),r.service_type&&(s=s.filter(n=>n.service_type===r.service_type)),r.partner_id&&(s=s.filter(n=>n.partner_id===parseInt(r.partner_id))),b.value){const n=b.value.toLowerCase();s=s.filter(c=>c.service_name.toLowerCase().includes(n)||c.partner_name.toLowerCase().includes(n)||c.service_type.toLowerCase().includes(n))}m.value&&s.sort((n,c)=>{const A=n[m.value],V=c[m.value],T=S.value==="asc"?1:-1;return A<V?-1*T:A>V?1*T:0}),C.value=s.length;const t=(d.value-1)*g.value,o=t+g.value;w.value=s.slice(t,o),k.value=M}catch(s){console.error("Error loading partner services:",s)}finally{i.value=!1}},Z=()=>{l()},z=s=>{d.value=s,l()},L=s=>{b.value=s,d.value=1,l()},j=(s,t)=>{m.value=s,S.value=t,l()},E=s=>{P(s)},y=()=>{d.value=1,l()},P=s=>{console.log("View service:",s)},F=s=>{console.log("Edit service:",s)},H=s=>{console.log("Delete service:",s)},I=()=>{console.log("Export partner services data")};return $(()=>{l()}),(s,t)=>(p(),u("div",W,[e("div",X,[e("div",Y,[t[7]||(t[7]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Partner Services"),e("p",{class:"mt-1 text-sm text-gray-600"}," Manage and monitor partner services and their configurations ")],-1)),e("div",ee,[e("button",{onClick:Z,disabled:i.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"},[i.value?(p(),u("svg",se,t[4]||(t[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):(p(),u("svg",oe,t[5]||(t[5]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))),x(" "+v(i.value?"Refreshing...":"Refresh"),1)],8,te),e("button",{onClick:I,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},t[6]||(t[6]=[e("svg",{class:"-ml-1 mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),x(" Export ")]))])])]),e("div",ne,[e("div",re,[e("div",null,[t[9]||(t[9]=e("label",{for:"status-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Status",-1)),_(e("select",{id:"status-filter","onUpdate:modelValue":t[0]||(t[0]=o=>r.status=o),onChange:y,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},t[8]||(t[8]=[e("option",{value:""},"All Statuses",-1),e("option",{value:"active"},"Active",-1),e("option",{value:"inactive"},"Inactive",-1),e("option",{value:"pending"},"Pending",-1)]),544),[[h,r.status]])]),e("div",null,[t[11]||(t[11]=e("label",{for:"service-type-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Service Type",-1)),_(e("select",{id:"service-type-filter","onUpdate:modelValue":t[1]||(t[1]=o=>r.service_type=o),onChange:y,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},t[10]||(t[10]=[q('<option value="">All Types</option><option value="betting">Betting</option><option value="payment">Payment</option><option value="analytics">Analytics</option><option value="integration">Integration</option>',5)]),544),[[h,r.service_type]])]),e("div",null,[t[13]||(t[13]=e("label",{for:"partner-filter",class:"block text-sm font-medium text-gray-700 mb-2"},"Partner",-1)),_(e("select",{id:"partner-filter","onUpdate:modelValue":t[2]||(t[2]=o=>r.partner_id=o),onChange:y,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"},[t[12]||(t[12]=e("option",{value:""},"All Partners",-1)),(p(!0),u(G,null,Q(k.value,o=>(p(),u("option",{key:o.id,value:o.id},v(o.name),9,ae))),128))],544),[[h,r.partner_id]])])])]),N(O,{data:w.value,loading:i.value,"current-page":d.value,"total-records":C.value,"page-size":g.value,title:"Partner Services","row-key":"id","has-actions":!0,onPageChange:z,onSearch:L,onSort:j,onRowClick:E},{"header-actions":f(()=>[e("div",ie,[e("button",{onClick:t[3]||(t[3]=o=>B.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},t[14]||(t[14]=[e("svg",{class:"-ml-0.5 mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),x(" Add Service ")]))])]),"cell-status":f(({value:o})=>[e("span",{class:J([{"bg-green-100 text-green-800":o==="active","bg-red-100 text-red-800":o==="inactive","bg-yellow-100 text-yellow-800":o==="pending"},"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},v(o?o.charAt(0).toUpperCase()+o.slice(1):"-"),3)]),"cell-service_type":f(({value:o})=>[e("span",le,v(o?o.charAt(0).toUpperCase()+o.slice(1):"-"),1)]),actions:f(({item:o})=>[e("div",de,[e("button",{onClick:n=>P(o),class:"text-blue-600 hover:text-blue-900 text-sm font-medium transition-colors duration-200"}," View ",8,ce),e("button",{onClick:n=>F(o),class:"text-indigo-600 hover:text-indigo-900 text-sm font-medium transition-colors duration-200"}," Edit ",8,ue),e("button",{onClick:n=>H(o),class:"text-red-600 hover:text-red-900 text-sm font-medium transition-colors duration-200"}," Delete ",8,pe)])]),_:1},8,["data","loading","current-page","total-records","page-size"])]))}});export{ge as default};
