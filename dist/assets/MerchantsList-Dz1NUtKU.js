import{d as ie,r as b,a as ce,x as w,p as de,A as ue,c as k,b as e,j as d,i as C,y as n,t as l,k as i,T as O,f as B,z as me,g as fe,h as x,v as y,o as M}from"./index-DOaBqVmr.js";import{_ as ge}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{_ as ve}from"./FilterCards.vue_vue_type_script_setup_true_lang-Bk3N6i80.js";import{m as A}from"./merchantsApi-DyJPmv37.js";import{u as pe}from"./useBackofficeActions-O4W2PmYt.js";import{r as be}from"./PlusIcon-BKTWa0k6.js";import{r as S}from"./BuildingOfficeIcon-Cd0d7gpT.js";const he={class:"space-y-6"},_e={class:"flex items-center justify-between"},xe={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},ye={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},we={class:"flex items-center"},ke={class:"flex-shrink-0"},Ce={class:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center"},Me={class:"ml-4"},Ae={class:"text-2xl font-bold text-gray-900"},Se={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},Ee={class:"flex items-center"},je={class:"flex-shrink-0"},Ve={class:"w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center"},De={class:"ml-4"},$e={class:"text-2xl font-bold text-gray-900"},Oe={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},Be={class:"flex items-center"},ze={class:"flex-shrink-0"},Le={class:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center"},Ne={class:"ml-4"},Pe={class:"text-2xl font-bold text-gray-900"},Te={class:"font-medium text-gray-900"},Ue={class:"text-sm"},Ie={class:"font-medium text-gray-900"},Re={class:"text-gray-500"},Fe={class:"text-sm"},He={class:"font-medium text-gray-900"},qe={class:"text-gray-500"},Qe={class:"text-sm text-gray-500"},Ge={class:"relative"},Je=["onClick"],Ke={key:0,class:"absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5"},We={class:"py-1"},Xe=["onClick"],Ye=["onClick"],Ze=["onClick"],et=["onClick"],tt=["onClick"],st={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},ot={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},nt={class:"mt-3"},at={class:"space-y-4"},lt={class:"flex justify-end space-x-3 mt-6"},rt=["disabled"],pt=ie({__name:"MerchantsList",setup(it){const{showDropdown:m,loading:u,currentPage:g,pageSize:E,totalRecords:h,searchQuery:z,formatCurrency:L,formatDate:N,formatTime:j,toggleDropdown:P,closeDropdown:T,goToPage:U,editRow:I,handleSearch:R}=pe(),r=b([]),V=b(""),D=b("asc"),_=b(!1),v=ce({status:"",client_id:"",phone_number:"",customer_name:""}),F=[{value:"1",label:"Active"},{value:"0",label:"Inactive"}],a=b({client_name:"",client_phone:"",client_email:"",client_address:""}),H={client_account:"Account",client_name:"Merchant Name",client_phone:"Contact Info",total_loan_assets:"Loan Assets",open_date:"Operating Hours",b2c_paybill:"PayBill Numbers",client_status:"Status",created:"Created Date"},q=w(()=>r.value.filter(s=>s.client_status===1).length),Q=w(()=>r.value.filter(s=>s.client_status===0).length),G=w(()=>r.value.filter(s=>s.can_issue_loans==="1").length),c=async(s={})=>{u.value=!0;try{const t=await A.getMerchants({page:g.value,limit:E.value,search:z.value,sortField:V.value,sortDirection:D.value,...s});t.status===200?(r.value=t.message.data||[],h.value=t.message.total_count||0):(console.error("Error fetching merchants:",t.message),r.value=[],h.value=0)}catch(t){console.error("Error fetching merchants:",t),r.value=[],h.value=0}finally{u.value=!1}},J=s=>{Object.assign(v,s)},K=s=>{Object.assign(v,s),g.value=1,c()},W=()=>{Object.keys(v).forEach(s=>{v[s]=""}),g.value=1,c()},X=s=>{U(s,c)},Y=s=>{R(s,c)},Z=(s,t)=>{V.value=s,D.value=t,g.value=1,c()},ee=s=>{console.log("Row clicked:",s)},te=()=>{c()},se=async()=>{if(!(!a.value.client_name||!a.value.client_phone||!a.value.client_email)){u.value=!0;try{const s=await A.addMerchant(a.value);s.status===200?(_.value=!1,a.value={client_name:"",client_phone:"",client_email:"",client_address:""},c()):console.error("Error adding merchant:",s.message)}catch(s){console.error("Error adding merchant:",s)}finally{u.value=!1}}},oe=async s=>{await I(s,"merchants-edit")},ne=async s=>{const t=s.client_status===1?0:1;try{const o=await A.updateMerchant({client_account:s.client_account,client_status:t});o.status===200?s.client_status=t:console.error("Error updating merchant status:",o.message)}catch(o){console.error("Error updating merchant status:",o)}T()},ae=s=>{console.log("View transactions for:",s.client_name),m[r.value.indexOf(s)]=!1},le=s=>{console.log("View loan products for:",s.client_name),m[r.value.indexOf(s)]=!1},re=s=>{console.log("View system users for:",s.client_name),m[r.value.indexOf(s)]=!1},$=s=>{s.target.closest(".relative")||Object.keys(m).forEach(o=>{m[parseInt(o)]=!1})};return de(()=>{c(),document.addEventListener("click",$)}),ue(()=>{document.removeEventListener("click",$)}),(s,t)=>(M(),k("div",he,[e("div",_e,[t[7]||(t[7]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Merchants"),e("p",{class:"mt-1 text-sm text-gray-500"}," Manage and view all registered merchants ")],-1)),e("button",{onClick:t[0]||(t[0]=o=>_.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[d(n(be),{class:"h-4 w-4 mr-2"}),t[6]||(t[6]=C(" Add Merchant "))])]),e("div",xe,[e("div",ye,[e("div",we,[e("div",ke,[e("div",Ce,[d(n(S),{class:"w-5 h-5 text-green-600"})])]),e("div",Me,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-500"},"Active Merchants",-1)),e("p",Ae,l(q.value),1)])])]),e("div",Se,[e("div",Ee,[e("div",je,[e("div",Ve,[d(n(S),{class:"w-5 h-5 text-red-600"})])]),e("div",De,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-500"},"Inactive Merchants",-1)),e("p",$e,l(Q.value),1)])])]),e("div",Oe,[e("div",Be,[e("div",ze,[e("div",Le,[d(n(S),{class:"w-5 h-5 text-blue-600"})])]),e("div",Ne,[t[10]||(t[10]=e("p",{class:"text-sm font-medium text-gray-500"},"Can Issue Loans",-1)),e("p",Pe,l(G.value),1)])])])]),d(ve,{filters:v,"filter-type":"merchants","status-options":F,"onUpdate:filters":J,onApply:K,onClear:W},null,8,["filters"]),d(ge,{data:r.value,headers:H,loading:n(u),"current-page":n(g),"total-records":n(h),"page-size":n(E),title:"Merchants","row-key":"client_id","has-actions":!0,onPageChange:X,onSearch:Y,onSort:Z,onRowClick:ee},{"header-actions":i(()=>[e("button",{onClick:te,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},t[11]||(t[11]=[e("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),C(" Refresh ")]))]),"cell-client_status":i(({value:o})=>[e("span",{class:me(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",{"bg-green-100 text-green-800":o===1,"bg-red-100 text-red-800":o===0}])},l(o===1?"Active":"Inactive"),3)]),"cell-total_loan_assets":i(({value:o})=>[e("span",Te,l(n(L)(o)),1)]),"cell-open_date":i(({value:o,item:f})=>[e("div",Ue,[e("div",Ie,l(n(j)(o)),1),e("div",Re,"to "+l(n(j)(f.close_date)),1)])]),"cell-b2c_paybill":i(({value:o,item:f})=>[e("div",Fe,[e("div",He,"B2C: "+l(o),1),e("div",qe,"C2B: "+l(f.c2b_paybill),1)])]),"cell-created":i(({value:o})=>[e("span",Qe,l(n(N)(o)),1)]),actions:i(({item:o,index:f})=>[e("div",Ge,[e("button",{onClick:p=>n(P)(f),class:"inline-flex items-center p-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700"},t[12]||(t[12]=[C(" Actions "),e("svg",{class:"w-4 h-4 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})],-1)]),8,Je),d(O,{"enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:i(()=>[n(m)[f]?(M(),k("div",Ke,[e("div",We,[e("button",{onClick:p=>oe(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Edit Merchant ",8,Xe),e("button",{onClick:p=>ne(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"},l(o.client_status===1?"Deactivate":"Activate"),9,Ye),e("button",{onClick:p=>ae(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," View Transactions ",8,Ze),e("button",{onClick:p=>le(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Loan Products ",8,et),e("button",{onClick:p=>re(o),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," System Users ",8,tt)])])):B("",!0)]),_:2},1024)])]),_:1},8,["data","loading","current-page","total-records","page-size"]),d(O,{"enter-active-class":"transition ease-out duration-300","enter-from-class":"opacity-0","enter-to-class":"opacity-100","leave-active-class":"transition ease-in duration-200","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:i(()=>[_.value?(M(),k("div",st,[e("div",ot,[e("div",nt,[t[17]||(t[17]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Add New Merchant",-1)),e("form",{onSubmit:fe(se,["prevent"])},[e("div",at,[e("div",null,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700"},"Merchant Name",-1)),x(e("input",{"onUpdate:modelValue":t[1]||(t[1]=o=>a.value.client_name=o),type:"text",required:"",class:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,512),[[y,a.value.client_name]])]),e("div",null,[t[14]||(t[14]=e("label",{class:"block text-sm font-medium text-gray-700"},"Phone",-1)),x(e("input",{"onUpdate:modelValue":t[2]||(t[2]=o=>a.value.client_phone=o),type:"tel",required:"",class:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,512),[[y,a.value.client_phone]])]),e("div",null,[t[15]||(t[15]=e("label",{class:"block text-sm font-medium text-gray-700"},"Email",-1)),x(e("input",{"onUpdate:modelValue":t[3]||(t[3]=o=>a.value.client_email=o),type:"email",required:"",class:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,512),[[y,a.value.client_email]])]),e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700"},"Address",-1)),x(e("textarea",{"onUpdate:modelValue":t[4]||(t[4]=o=>a.value.client_address=o),rows:"3",class:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"},null,512),[[y,a.value.client_address]])])]),e("div",lt,[e("button",{type:"button",onClick:t[5]||(t[5]=o=>_.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"}," Cancel "),e("button",{type:"submit",disabled:n(u),class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},l(n(u)?"Adding...":"Add Merchant"),9,rt)])],32)])])])):B("",!0)]),_:1})]))}});export{pt as default};
