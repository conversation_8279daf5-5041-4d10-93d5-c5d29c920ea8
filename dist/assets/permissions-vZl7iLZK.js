const i={DASHBOARD:"dashboard",CLIENTS:"clients",LOANS:"loans",TRANSACTIONS:"transactions",REPORTS:"reports",SYSTEM:"system",SETTINGS:"settings"},e={DASHBOARD_VIEW:{id:1,name:"dashboard.view",module:i.DASHBOARD,description:"View dashboard"},DASHBOARD_ANALYTICS:{id:2,name:"dashboard.analytics",module:i.DASHBOARD,description:"View analytics"},CLIENTS_VIEW:{id:10,name:"clients.view",module:i.CLIENTS,description:"View clients"},CLIENTS_CREATE:{id:11,name:"clients.create",module:i.CLIENTS,description:"Create clients"},CLIENTS_EDIT:{id:12,name:"clients.edit",module:i.CLIENTS,description:"Edit clients"},CLIENTS_DELETE:{id:13,name:"clients.delete",module:i.CLIENTS,description:"Delete clients"},CLIENTS_EXPORT:{id:14,name:"clients.export",module:i.CLIENTS,description:"Export client data"},LOANS_VIEW:{id:20,name:"loans.view",module:i.LOANS,description:"View loans"},LOANS_REQUESTS_VIEW:{id:21,name:"loans.requests.view",module:i.LOANS,description:"View loan requests"},LOANS_REQUESTS_APPROVE:{id:22,name:"loans.requests.approve",module:i.LOANS,description:"Approve loan requests"},LOANS_REQUESTS_REJECT:{id:23,name:"loans.requests.reject",module:i.LOANS,description:"Reject loan requests"},LOANS_LIMITS_VIEW:{id:24,name:"loans.limits.view",module:i.LOANS,description:"View loan limits"},LOANS_LIMITS_MANAGE:{id:25,name:"loans.limits.manage",module:i.LOANS,description:"Manage loan limits"},LOANS_ACCOUNTS_VIEW:{id:26,name:"loans.accounts.view",module:i.LOANS,description:"View loan accounts"},LOANS_ACCOUNTS_MANAGE:{id:27,name:"loans.accounts.manage",module:i.LOANS,description:"Manage loan accounts"},LOANS_REPAYMENTS_VIEW:{id:28,name:"loans.repayments.view",module:i.LOANS,description:"View loan repayments"},LOANS_REPAYMENTS_VERIFY:{id:29,name:"loans.repayments.verify",module:i.LOANS,description:"Verify loan repayments"},LOANS_PRODUCTS_VIEW:{id:30,name:"loans.products.view",module:i.LOANS,description:"View loan products"},LOANS_PRODUCTS_MANAGE:{id:31,name:"loans.products.manage",module:i.LOANS,description:"Manage loan products"},TRANSACTIONS_VIEW:{id:40,name:"transactions.view",module:i.TRANSACTIONS,description:"View transactions"},TRANSACTIONS_EXPORT:{id:41,name:"transactions.export",module:i.TRANSACTIONS,description:"Export transactions"},TRANSACTIONS_RECONCILE:{id:42,name:"transactions.reconcile",module:i.TRANSACTIONS,description:"Reconcile transactions"},REPORTS_VIEW:{id:50,name:"reports.view",module:i.REPORTS,description:"View reports"},REPORTS_GENERATE:{id:51,name:"reports.generate",module:i.REPORTS,description:"Generate reports"},REPORTS_EXPORT:{id:52,name:"reports.export",module:i.REPORTS,description:"Export reports"},REPORTS_SCHEDULE:{id:53,name:"reports.schedule",module:i.REPORTS,description:"Schedule reports"},SYSTEM_USERS_VIEW:{id:60,name:"system.users.view",module:i.SYSTEM,description:"View system users"},SYSTEM_USERS_CREATE:{id:61,name:"system.users.create",module:i.SYSTEM,description:"Create system users"},SYSTEM_USERS_EDIT:{id:62,name:"system.users.edit",module:i.SYSTEM,description:"Edit system users"},SYSTEM_USERS_DELETE:{id:63,name:"system.users.delete",module:i.SYSTEM,description:"Delete system users"},SYSTEM_ROLES_VIEW:{id:64,name:"system.roles.view",module:i.SYSTEM,description:"View system roles"},SYSTEM_ROLES_CREATE:{id:65,name:"system.roles.create",module:i.SYSTEM,description:"Create system roles"},SYSTEM_ROLES_EDIT:{id:66,name:"system.roles.edit",module:i.SYSTEM,description:"Edit system roles"},SYSTEM_ROLES_DELETE:{id:67,name:"system.roles.delete",module:i.SYSTEM,description:"Delete system roles"},SYSTEM_PERMISSIONS_VIEW:{id:68,name:"system.permissions.view",module:i.SYSTEM,description:"View permissions"},SYSTEM_AUDIT_VIEW:{id:69,name:"system.audit.view",module:i.SYSTEM,description:"View audit logs"},SETTINGS_VIEW:{id:70,name:"settings.view",module:i.SETTINGS,description:"View settings"},SETTINGS_EDIT:{id:71,name:"settings.edit",module:i.SETTINGS,description:"Edit settings"},SETTINGS_SYSTEM:{id:72,name:"settings.system",module:i.SETTINGS,description:"Manage system settings"}},E={SUPER_ADMIN:{id:1,name:"Super Admin",description:"Full system access",permissions:Object.values(e).map(S=>S.id)},ADMIN:{id:2,name:"Administrator",description:"Administrative access",permissions:[e.DASHBOARD_VIEW.id,e.DASHBOARD_ANALYTICS.id,e.CLIENTS_VIEW.id,e.CLIENTS_CREATE.id,e.CLIENTS_EDIT.id,e.CLIENTS_EXPORT.id,e.LOANS_VIEW.id,e.LOANS_REQUESTS_VIEW.id,e.LOANS_REQUESTS_APPROVE.id,e.LOANS_REQUESTS_REJECT.id,e.LOANS_LIMITS_VIEW.id,e.LOANS_LIMITS_MANAGE.id,e.LOANS_ACCOUNTS_VIEW.id,e.LOANS_ACCOUNTS_MANAGE.id,e.LOANS_REPAYMENTS_VIEW.id,e.LOANS_REPAYMENTS_VERIFY.id,e.LOANS_PRODUCTS_VIEW.id,e.LOANS_PRODUCTS_MANAGE.id,e.TRANSACTIONS_VIEW.id,e.TRANSACTIONS_EXPORT.id,e.TRANSACTIONS_RECONCILE.id,e.REPORTS_VIEW.id,e.REPORTS_GENERATE.id,e.REPORTS_EXPORT.id,e.SYSTEM_USERS_VIEW.id,e.SYSTEM_USERS_CREATE.id,e.SYSTEM_USERS_EDIT.id,e.SYSTEM_ROLES_VIEW.id,e.SETTINGS_VIEW.id,e.SETTINGS_EDIT.id]},MANAGER:{id:3,name:"Manager",description:"Management access",permissions:[e.DASHBOARD_VIEW.id,e.DASHBOARD_ANALYTICS.id,e.CLIENTS_VIEW.id,e.CLIENTS_EDIT.id,e.CLIENTS_EXPORT.id,e.LOANS_VIEW.id,e.LOANS_REQUESTS_VIEW.id,e.LOANS_REQUESTS_APPROVE.id,e.LOANS_REQUESTS_REJECT.id,e.LOANS_LIMITS_VIEW.id,e.LOANS_LIMITS_MANAGE.id,e.LOANS_ACCOUNTS_VIEW.id,e.LOANS_REPAYMENTS_VIEW.id,e.LOANS_REPAYMENTS_VERIFY.id,e.LOANS_PRODUCTS_VIEW.id,e.TRANSACTIONS_VIEW.id,e.TRANSACTIONS_EXPORT.id,e.REPORTS_VIEW.id,e.REPORTS_GENERATE.id,e.REPORTS_EXPORT.id,e.SETTINGS_VIEW.id]},OPERATOR:{id:4,name:"Operator",description:"Operational access",permissions:[e.DASHBOARD_VIEW.id,e.CLIENTS_VIEW.id,e.LOANS_VIEW.id,e.LOANS_REQUESTS_VIEW.id,e.LOANS_LIMITS_VIEW.id,e.LOANS_ACCOUNTS_VIEW.id,e.LOANS_REPAYMENTS_VIEW.id,e.LOANS_PRODUCTS_VIEW.id,e.TRANSACTIONS_VIEW.id,e.REPORTS_VIEW.id]},VIEWER:{id:5,name:"Viewer",description:"Read-only access",permissions:[e.DASHBOARD_VIEW.id,e.CLIENTS_VIEW.id,e.LOANS_VIEW.id,e.LOANS_REQUESTS_VIEW.id,e.LOANS_LIMITS_VIEW.id,e.LOANS_ACCOUNTS_VIEW.id,e.LOANS_REPAYMENTS_VIEW.id,e.LOANS_PRODUCTS_VIEW.id,e.TRANSACTIONS_VIEW.id,e.REPORTS_VIEW.id]}};export{i as P,E as R};
