import{d as g,u as h,r as i,a as w,c as l,b as e,e as _,g as y,f as u,t as c,h as k,v as S,i as m,j as C,k as j,l as B,n as M,o as d}from"./index-DOaBqVmr.js";const V={class:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8"},N={class:"max-w-md w-full space-y-8"},R={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},A={key:0,class:"mb-4 p-4 rounded-md bg-red-50 border border-red-200"},E={class:"flex"},F={class:"ml-3"},P={class:"text-sm text-red-800"},T={key:1,class:"mb-4 p-4 rounded-md bg-green-50 border border-green-200"},q={class:"flex"},D={class:"ml-3"},H={class:"text-sm text-green-800"},U={class:"space-y-4"},z={class:"mt-6"},I=["disabled"],L={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},G={class:"mt-4 text-center"},O=g({__name:"ForgotPassword",setup(J){const p=M(),v=h(),s=i(!1),o=i(""),n=i(""),a=w({username:""}),x=async()=>{o.value="",n.value="",s.value=!0;try{const r=await v.forgotPassword(a.username);r.success?(n.value="Reset code sent to your phone. Redirecting...",setTimeout(()=>{p.push({name:"reset-password",query:{username:a.username}})},2e3)):o.value=r.message||"Failed to send reset code"}catch(r){o.value=r.message||"An unexpected error occurred"}finally{s.value=!1}};return(r,t)=>{const b=B("router-link");return d(),l("div",V,[e("div",N,[t[6]||(t[6]=_('<div class="text-center"><div class="mx-auto h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg"><span class="text-white font-bold text-xl">S</span></div><h2 class="mt-6 text-3xl font-extrabold text-gray-900"> Forgot Password </h2><p class="mt-2 text-sm text-gray-600"> Enter your username to reset your password </p></div>',1)),e("form",{class:"mt-8 space-y-6",onSubmit:y(x,["prevent"])},[e("div",R,[o.value?(d(),l("div",A,[e("div",E,[t[1]||(t[1]=e("svg",{class:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",F,[e("p",P,c(o.value),1)])])])):u("",!0),n.value?(d(),l("div",T,[e("div",q,[t[2]||(t[2]=e("svg",{class:"h-5 w-5 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)),e("div",D,[e("p",H,c(n.value),1)])])])):u("",!0),e("div",U,[e("div",null,[t[3]||(t[3]=e("label",{for:"username",class:"block text-sm font-medium text-gray-700"}," Username ",-1)),k(e("input",{id:"username","onUpdate:modelValue":t[0]||(t[0]=f=>a.username=f),type:"text",required:"",class:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your username"},null,512),[[S,a.username]])])]),e("div",z,[e("button",{type:"submit",disabled:s.value,class:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"},[s.value?(d(),l("svg",L,t[4]||(t[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))):u("",!0),m(" "+c(s.value?"Sending...":"Send Reset Code"),1)],8,I)]),e("div",G,[C(b,{to:{name:"login"},class:"text-sm text-blue-600 hover:text-blue-500 transition-colors duration-200"},{default:j(()=>t[5]||(t[5]=[m(" Back to Sign In ")])),_:1,__:[5]})])])],32)])])}}});export{O as default};
