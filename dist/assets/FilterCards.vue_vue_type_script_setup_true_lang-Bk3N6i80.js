import{d as B,u as $,a as V,x as m,w as z,p as E,c as r,F as k,b as t,f as i,j as d,y,J as g,z as v,T as x,k as h,t as C,i as T,h as u,C as _,m as D,v as p,o as n}from"./index-DOaBqVmr.js";const P={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6"},J={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-4"},G={class:"flex items-center justify-between mb-3"},H={key:0,class:"overflow-hidden"},K=["value"],Q={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-4"},W={class:"flex items-center justify-between mb-3"},X={key:0,class:"overflow-hidden"},Y=["value"],Z={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-4"},ee={class:"flex items-center justify-between mb-3"},te={class:"text-sm font-medium text-gray-700"},se={key:0,class:"overflow-hidden space-y-2"},oe={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-4"},re={class:"flex items-center justify-between mb-3"},ne={key:0,class:"overflow-hidden space-y-2"},le={key:0,class:"bg-white rounded-lg border border-gray-200 shadow-sm"},ae={class:"p-4"},ie={key:0,class:"overflow-hidden space-y-2 px-4 pb-4"},ue={class:"grid grid-cols-1 md:grid-cols-2 gap-2"},ce=B({__name:"FilterCards",props:{filters:{default:()=>({})},filterType:{default:"requests"},statusOptions:{default:()=>[{value:"1",label:"Active"},{value:"0",label:"Inactive"},{value:"2",label:"Pending"},{value:"3",label:"Rejected"}]}},emits:["update:filters","apply","clear"],setup(I,{emit:N}){const f=I,w=N,j=$(),o=V({...f.filters}),l=V({status:!1,client:!1,account:!1,contact:!1,checkoff:!1}),F=m(()=>j.clientList||[]),U=m(()=>{switch(f.filterType){case"requests":return"Request Details";case"limits":return"Limit Details";case"accounts":return"Account Details";case"repayments":return"Repayment Details";case"merchants":return"Merchant Details";case"checkoff":return"CheckOff Details";default:return"Details"}}),q=m(()=>["requests","accounts","repayments"].includes(f.filterType)),S=m(()=>["requests","limits"].includes(f.filterType)),A=m(()=>f.filterType==="repayments"),L=m(()=>["requests","accounts","merchants"].includes(f.filterType)),M=m(()=>f.filterType==="accounts"),b=c=>{l[c]=!l[c]},a=()=>{w("update:filters",{...o})},R=()=>{w("apply",{...o})},O=()=>{Object.keys(o).forEach(c=>{o[c]=""}),w("clear"),w("update:filters",{...o})};return z(()=>f.filters,c=>{Object.assign(o,c)},{deep:!0}),E(()=>{l.status=!0}),(c,e)=>(n(),r(k,null,[t("div",P,[t("div",J,[t("div",G,[e[16]||(e[16]=t("h3",{class:"text-sm font-medium text-gray-700"},"Status",-1)),t("button",{onClick:e[0]||(e[0]=s=>b("status")),class:"text-gray-400 hover:text-gray-600 transition-colors"},[d(y(g),{class:v(["w-4 h-4 transition-transform duration-200",{"rotate-180":l.status}])},null,8,["class"])])]),d(x,{"enter-active-class":"transition-all duration-200 ease-out","enter-from-class":"opacity-0 max-h-0","enter-to-class":"opacity-100 max-h-40","leave-active-class":"transition-all duration-200 ease-in","leave-from-class":"opacity-100 max-h-40","leave-to-class":"opacity-0 max-h-0"},{default:h(()=>[l.status?(n(),r("div",H,[u(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>o.status=s),onChange:a,class:"w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},[e[17]||(e[17]=t("option",{value:""},"All Status",-1)),(n(!0),r(k,null,D(c.statusOptions,s=>(n(),r("option",{key:s.value,value:s.value},C(s.label),9,K))),128))],544),[[_,o.status]])])):i("",!0)]),_:1})]),t("div",Q,[t("div",W,[e[18]||(e[18]=t("h3",{class:"text-sm font-medium text-gray-700"},"Client",-1)),t("button",{onClick:e[2]||(e[2]=s=>b("client")),class:"text-gray-400 hover:text-gray-600 transition-colors"},[d(y(g),{class:v(["w-4 h-4 transition-transform duration-200",{"rotate-180":l.client}])},null,8,["class"])])]),d(x,{"enter-active-class":"transition-all duration-200 ease-out","enter-from-class":"opacity-0 max-h-0","enter-to-class":"opacity-100 max-h-40","leave-active-class":"transition-all duration-200 ease-in","leave-from-class":"opacity-100 max-h-40","leave-to-class":"opacity-0 max-h-0"},{default:h(()=>[l.client?(n(),r("div",X,[u(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>o.client_id=s),onChange:a,class:"w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},[e[19]||(e[19]=t("option",{value:""},"All Clients",-1)),(n(!0),r(k,null,D(F.value,s=>(n(),r("option",{key:s.client_id,value:s.client_id},C(s.client_name),9,Y))),128))],544),[[_,o.client_id]])])):i("",!0)]),_:1})]),t("div",Z,[t("div",ee,[t("h3",te,C(U.value),1),t("button",{onClick:e[4]||(e[4]=s=>b("account")),class:"text-gray-400 hover:text-gray-600 transition-colors"},[d(y(g),{class:v(["w-4 h-4 transition-transform duration-200",{"rotate-180":l.account}])},null,8,["class"])])]),d(x,{"enter-active-class":"transition-all duration-200 ease-out","enter-from-class":"opacity-0 max-h-0","enter-to-class":"opacity-100 max-h-40","leave-active-class":"transition-all duration-200 ease-in","leave-from-class":"opacity-100 max-h-40","leave-to-class":"opacity-0 max-h-0"},{default:h(()=>[l.account?(n(),r("div",se,[q.value?u((n(),r("input",{key:0,"onUpdate:modelValue":e[5]||(e[5]=s=>o.loan_number=s),onInput:a,type:"text",placeholder:"Loan Number",class:"w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,544)),[[p,o.loan_number]]):i("",!0),S.value?u((n(),r("input",{key:1,"onUpdate:modelValue":e[6]||(e[6]=s=>o.reference_id=s),onInput:a,type:"text",placeholder:"Reference ID",class:"w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,544)),[[p,o.reference_id]]):i("",!0),A.value?u((n(),r("input",{key:2,"onUpdate:modelValue":e[7]||(e[7]=s=>o.request_number=s),onInput:a,type:"text",placeholder:"Request Number",class:"w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,544)),[[p,o.request_number]]):i("",!0)])):i("",!0)]),_:1})]),t("div",oe,[t("div",re,[e[20]||(e[20]=t("h3",{class:"text-sm font-medium text-gray-700"},"Contact Details",-1)),t("button",{onClick:e[8]||(e[8]=s=>b("contact")),class:"text-gray-400 hover:text-gray-600 transition-colors"},[d(y(g),{class:v(["w-4 h-4 transition-transform duration-200",{"rotate-180":l.contact}])},null,8,["class"])])]),d(x,{"enter-active-class":"transition-all duration-200 ease-out","enter-from-class":"opacity-0 max-h-0","enter-to-class":"opacity-100 max-h-40","leave-active-class":"transition-all duration-200 ease-in","leave-from-class":"opacity-100 max-h-40","leave-to-class":"opacity-0 max-h-0"},{default:h(()=>[l.contact?(n(),r("div",ne,[L.value?u((n(),r("input",{key:0,"onUpdate:modelValue":e[9]||(e[9]=s=>o.phone_number=s),onInput:a,type:"text",placeholder:"Phone Number",class:"w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,544)),[[p,o.phone_number]]):i("",!0),M.value?u((n(),r("input",{key:1,"onUpdate:modelValue":e[10]||(e[10]=s=>o.national_id=s),onInput:a,type:"text",placeholder:"National ID",class:"w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,544)),[[p,o.national_id]]):i("",!0),u(t("input",{"onUpdate:modelValue":e[11]||(e[11]=s=>o.customer_name=s),onInput:a,type:"text",placeholder:"Customer Name",class:"w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,544),[[p,o.customer_name]])])):i("",!0)]),_:1})]),c.filterType==="checkoff"?(n(),r("div",le,[t("div",ae,[t("button",{onClick:e[12]||(e[12]=s=>b("checkoff")),class:"flex items-center justify-between w-full text-left"},[e[21]||(e[21]=t("h3",{class:"text-sm font-medium text-gray-900"},"CheckOff Filters",-1)),d(y(g),{class:v(["w-5 h-5 text-gray-500 transition-transform duration-200",{"rotate-180":l.checkoff}])},null,8,["class"])])]),d(x,{"enter-active-class":"transition-all duration-200 ease-out","enter-from-class":"opacity-0 max-h-0","enter-to-class":"opacity-100 max-h-40","leave-active-class":"transition-all duration-200 ease-in","leave-from-class":"opacity-100 max-h-40","leave-to-class":"opacity-0 max-h-0"},{default:h(()=>[l.checkoff?(n(),r("div",ie,[u(t("select",{"onUpdate:modelValue":e[13]||(e[13]=s=>o.type_id=s),onChange:a,class:"w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},e[22]||(e[22]=[t("option",{value:""},"All Types",-1),t("option",{value:"1"},"Type 1",-1),t("option",{value:"2"},"Type 2",-1)]),544),[[_,o.type_id]]),t("div",ue,[u(t("input",{"onUpdate:modelValue":e[14]||(e[14]=s=>o.start_date=s),onInput:a,type:"date",placeholder:"Start Date",class:"w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,544),[[p,o.start_date]]),u(t("input",{"onUpdate:modelValue":e[15]||(e[15]=s=>o.end_date=s),onInput:a,type:"date",placeholder:"End Date",class:"w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,544),[[p,o.end_date]])])])):i("",!0)]),_:1})])):i("",!0)]),t("div",{class:"flex flex-wrap gap-3 mb-6"},[t("button",{onClick:R,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},e[23]||(e[23]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"})],-1),T(" Apply Filters ")])),t("button",{onClick:O,class:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},e[24]||(e[24]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1),T(" Clear Filters ")]))])],64))}});export{ce as _};
