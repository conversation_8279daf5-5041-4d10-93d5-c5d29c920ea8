import{s as x}from"./systemApi-CvVfCghC.js";import{_ as y}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{_}from"./SearchFilter.vue_vue_type_script_setup_true_lang-jgd-6IIX.js";import{P as b}from"./permissions-vZl7iLZK.js";import{_ as w,c as n,b as a,j as h,i as f,l as g,k as l,t as c,o as m}from"./index-DOaBqVmr.js";import"./MagnifyingGlassIcon-D7MUVaIi.js";import"./XMarkIcon-CqcEu60T.js";const v={components:{DataTable:y,SearchFilter:_},data(){return{isOpen:!1,isLoading:!1,items:[],fullPage:!0,total:0,limit:10,offset:1,searchFilters:{search:"",status:"",module:""},tableHeaders:{index:"#",id:"ID",name:"Permission Name",status:"Status",created_at:"Created"}}},computed:{data(){return this.items},moduleOptions(){return Object.values(b).map(e=>({value:e,label:e.charAt(0).toUpperCase()+e.slice(1).replace(/[_-]/g," ")}))}},mounted(){this.getItems()},methods:{gotToPage(e){let t=this;t.offset=e,t.getItems()},async getItems(){var d;let e=this;e.isLoading=!0;let t={page:e.offset,per_page:e.limit};try{let i=await x.getPermissions(t);i.status===200&&(e.items=((d=i.message)==null?void 0:d.data)||i.message||[],e.total=e.items.length)}catch(i){console.error("Error fetching permissions:",i),e.items=[],e.total=0}e.isLoading=!1},handleSearch(e){this.searchFilters.search=e,this.applyFilters()},handleFilter(e){Object.assign(this.searchFilters,e),this.applyFilters()},handleClearFilters(){this.searchFilters={search:"",status:"",module:""},this.applyFilters()},applyFilters(){this.offset=1,this.getItems()},handleRowClick(e,t){console.log("Row clicked:",e,t)},handleLimitChange(e){this.limit=e,this.offset=1,this.getItems()},formatDate(e){if(!e)return"N/A";try{return new Date(e).toLocaleString()}catch{return"N/A"}}}},C={class:"space-y-6"},k={class:"flex items-center justify-between"},F={class:"flex items-center space-x-3"},S={class:"text-gray-900 font-medium"},I={class:"text-gray-900 font-bold"},P={class:"text-gray-900 font-medium"},D={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},L={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800"},N={key:2,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800"},R={class:"text-gray-900"};function A(e,t,d,i,o,r){const u=g("SearchFilter"),p=g("DataTable");return m(),n("div",C,[a("div",k,[t[4]||(t[4]=a("div",null,[a("h1",{class:"text-2xl font-bold text-gray-900"},"System Permissions"),a("p",{class:"mt-1 text-sm text-gray-500"}," Manage system permissions and access controls ")],-1)),a("div",F,[a("button",{onClick:t[0]||(t[0]=(...s)=>r.getItems&&r.getItems(...s)),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[3]||(t[3]=[a("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),f(" Refresh ")]))])]),h(u,{filters:o.searchFilters,"onUpdate:filters":t[1]||(t[1]=s=>o.searchFilters=s),"search-placeholder":"Search permissions by name or description...","show-status-filter":!0,"show-role-filter":!1,"show-module-filter":!0,"show-date-filter":!1,"show-client-filter":!1,"has-advanced-filters":!1,"module-options":r.moduleOptions,onSearch:r.handleSearch,onFilter:r.handleFilter,onClear:r.handleClearFilters},null,8,["filters","module-options","onSearch","onFilter","onClear"]),h(p,{data:r.data,headers:o.tableHeaders,title:"System Permissions",loading:o.isLoading,searchable:!0,pagination:!0,"current-page":o.offset,"total-records":o.total,"page-size":o.limit,"has-actions":!1,"empty-message":"No permissions found",onPageChange:r.gotToPage,onSearch:r.handleSearch,onRowClick:r.handleRowClick,onLimitChange:r.handleLimitChange},{"header-actions":l(()=>[a("button",{onClick:t[2]||(t[2]=(...s)=>r.getItems&&r.getItems(...s)),class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[5]||(t[5]=[a("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),f(" Refresh ")]))]),"cell-index":l(({index:s})=>[a("span",S,c(s+1),1)]),"cell-id":l(({item:s})=>[a("span",I,c(s.id),1)]),"cell-name":l(({item:s})=>[a("span",P,c(s.name),1)]),"cell-status":l(({item:s})=>[parseInt(s.status)===1?(m(),n("span",D," Active ")):parseInt(s.status)===3?(m(),n("span",L," Deactivated ")):(m(),n("span",N," Inactive "))]),"cell-created_at":l(({item:s})=>[a("span",R,c(r.formatDate(s.created_at)),1)]),_:1},8,["data","headers","loading","current-page","total-records","page-size","onPageChange","onSearch","onRowClick","onLimitChange"])])}const U=w(v,[["render",A]]);export{U as default};
