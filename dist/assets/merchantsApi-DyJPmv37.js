import{K as o,G as n}from"./index-DOaBqVmr.js";const p={async getMerchants(a={}){try{const e={limit:a.limit||10,offset:a.offset||0,page:a.page||1,status:"1",...a},s=o(e),t=(await n.post("merchant/v1/view/companies",e,{headers:{"X-Hash-Key":s}})).data.data,c={data:t.data||[],total_count:t.total_count||0,current_page:t.current_page||1,per_page:t.per_page||10,last_page:t.last_page||1,from:t.from||0,to:t.to||0};return{status:t.code,message:c,code:t.code.toString()}}catch(e){if(console.error("Merchants API error:",e),e.response){const s=e.response.status,r=e.response.data;if(s===422||s===500)return{status:s,message:{data:[],total_count:0,current_page:1,per_page:10},code:s.toString()};if(r.data)return{status:r.data.code||s,message:{data:[],total_count:0,current_page:1,per_page:10},code:(r.data.code||s).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:10},code:"500"}}},async addMerchant(a){try{const e={company_name:a.client_name,company_phone:a.client_phone,company_email:a.client_email,company_address:a.client_address,contact_person:a.client_name,dial_code:"254"},s=o(e),t=(await n.post("merchant/v1/create_account",e,{headers:{"X-Hash-Key":s}})).data.data;return{status:t.code,message:t.message||t,code:t.code.toString()}}catch(e){if(console.error("Add merchant error:",e),e.response){const s=e.response.status,r=e.response.data;return{status:s,message:r.statusDescription||"Error adding merchant",code:s.toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async updateMerchant(a){try{const e={client_account:a.client_account,status:a.client_status},s=o(e),t=(await n.post("merchant/v1/activate",e,{headers:{"X-Hash-Key":s}})).data.data;return{status:t.code,message:t.message||t,code:t.code.toString()}}catch(e){if(console.error("Update merchant error:",e),e.response){const s=e.response.status,r=e.response.data;return{status:s,message:r.statusDescription||"Error updating merchant",code:s.toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async getMerchantDetails(a){try{const e={client_account:a},s=o(e),t=(await n.post("merchant/v1/view/company_details",e,{headers:{"X-Hash-Key":s}})).data.data;return{status:t.code,message:t.data||t,code:t.code.toString()}}catch(e){if(console.error("Get merchant details error:",e),e.response){const s=e.response.status,r=e.response.data;return{status:s,message:r.statusDescription||"Error fetching merchant details",code:s.toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}}};export{p as m};
