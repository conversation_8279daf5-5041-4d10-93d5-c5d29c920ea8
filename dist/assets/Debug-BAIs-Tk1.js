import{_ as T}from"./NetworkDebugger.vue_vue_type_script_setup_true_lang-Bdso7PXR.js";import{d as I,r as p,a as v,p as A,c as u,b as e,j as x,i,t as o,f as _,F as w,m as h,G as k,s as C,z as V,o as g}from"./index-DOaBqVmr.js";import"./organizationsApi-CM42ShLT.js";import"./clientsApi-DPSRLazl.js";import"./merchantsApi-DyJPmv37.js";const S={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},P={class:"space-y-6"},D={class:"bg-white p-6 rounded-lg shadow border"},O={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},B={class:"bg-gray-50 p-3 rounded text-sm space-y-1"},$={class:"bg-gray-50 p-3 rounded text-sm space-y-1"},M={class:"mt-6"},N={class:"bg-gray-50 p-3 rounded text-sm space-y-1"},R={class:"mt-6"},z={class:"space-y-2"},U=["disabled"],F=["disabled"],K=["disabled"],Y=["disabled"],L={key:0,class:"mt-4"},H={class:"bg-gray-50 p-3 rounded max-h-40 overflow-y-auto"},G={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},j={class:"list-decimal list-inside text-sm text-blue-800 space-y-1"},tt=I({__name:"Debug",setup(q){const n=p(!1),m=p([]),a=v({VITE_BASE_API:void 0,VITE_APP_KEY:void 0,VITE_AUTH_KEY:void 0,VITE_DEV_MODE:void 0}),d=v({token:localStorage.getItem("token"),user:localStorage.getItem("user"),selectedClientId:localStorage.getItem("selectedClientId"),clientMode:localStorage.getItem("clientMode")}),r=(s,t)=>{m.value.unshift({timestamp:new Date().toLocaleTimeString(),message:s,success:t})},b=async()=>{n.value=!0;try{console.log("🧪 Testing basic connectivity...");const s=await fetch(a.VITE_BASE_API,{method:"HEAD",mode:"no-cors"});r("Basic connectivity test completed",!0)}catch(s){console.error("Basic connectivity test failed:",s),r(`Basic connectivity failed: ${s}`,!1)}finally{n.value=!1}},f=async()=>{n.value=!0;try{console.log("🧪 Testing CORS...");const s=await fetch(`${a.VITE_BASE_API}health`,{method:"GET",headers:{"Content-Type":"application/json"}});r(`CORS test: ${s.status} ${s.statusText}`,s.ok)}catch(s){console.error("CORS test failed:",s),r(`CORS test failed: ${s}`,!1)}finally{n.value=!1}},y=async()=>{var s;n.value=!0;try{console.log("🧪 Testing authentication...");const t=await k.post("merchant/v1/view/companies",{limit:1,offset:0,page:1,status:"1"});r(`Authentication test: ${t.status} ${t.statusText}`,t.status===200)}catch(t){console.error("Authentication test failed:",t);const l=((s=t.response)==null?void 0:s.status)||"Unknown";r(`Authentication test failed: ${l} - ${t.message}`,!1)}finally{n.value=!1}},E=async()=>{var s,t;n.value=!0;try{console.log("🧪 Testing Organizations from Auth file...");const l=await C.getOrganizationsDebug({limit:5});r(`Organizations (Auth File): Success - ${((s=l.message.data)==null?void 0:s.length)||0} records`,l.status===200)}catch(l){console.error("Organizations (Auth File) test failed:",l);const c=((t=l.response)==null?void 0:t.status)||"Unknown";r(`Organizations (Auth File) failed: ${c} - ${l.message}`,!1)}finally{n.value=!1}};return A(()=>{console.log("🔧 Debug page mounted"),console.log("Environment:",a),console.log("Local Storage:",d)}),(s,t)=>(g(),u("div",S,[e("div",P,[t[24]||(t[24]=e("div",null,[e("h1",{class:"text-3xl font-bold text-gray-900"},"Network Debug Page"),e("p",{class:"mt-2 text-gray-600"}," Debug API calls and network issues for organizations/clients migration ")],-1)),x(T),e("div",D,[t[16]||(t[16]=e("h2",{class:"text-xl font-semibold mb-4"},"Debug Information",-1)),e("div",O,[e("div",null,[t[4]||(t[4]=e("h3",{class:"font-medium mb-2"},"Environment Variables",-1)),e("div",B,[e("div",null,[t[0]||(t[0]=e("strong",null,"VITE_BASE_API:",-1)),i(" "+o(a.VITE_BASE_API),1)]),e("div",null,[t[1]||(t[1]=e("strong",null,"VITE_APP_KEY:",-1)),i(" "+o(a.VITE_APP_KEY?"***"+a.VITE_APP_KEY.slice(-4):"Not set"),1)]),e("div",null,[t[2]||(t[2]=e("strong",null,"VITE_AUTH_KEY:",-1)),i(" "+o(a.VITE_AUTH_KEY?"***"+a.VITE_AUTH_KEY.slice(-4):"Not set"),1)]),e("div",null,[t[3]||(t[3]=e("strong",null,"VITE_DEV_MODE:",-1)),i(" "+o(a.VITE_DEV_MODE),1)])])]),e("div",null,[t[9]||(t[9]=e("h3",{class:"font-medium mb-2"},"Local Storage",-1)),e("div",$,[e("div",null,[t[5]||(t[5]=e("strong",null,"Token:",-1)),i(" "+o(d.token?"Present":"Missing"),1)]),e("div",null,[t[6]||(t[6]=e("strong",null,"User:",-1)),i(" "+o(d.user?"Present":"Missing"),1)]),e("div",null,[t[7]||(t[7]=e("strong",null,"Selected Client ID:",-1)),i(" "+o(d.selectedClientId||"Not set"),1)]),e("div",null,[t[8]||(t[8]=e("strong",null,"Client Mode:",-1)),i(" "+o(d.clientMode||"Not set"),1)])])])]),e("div",M,[t[13]||(t[13]=e("h3",{class:"font-medium mb-2"},"Browser Information",-1)),e("div",N,[e("div",null,[t[10]||(t[10]=e("strong",null,"User Agent:",-1)),i(" "+o(s.navigator.userAgent),1)]),e("div",null,[t[11]||(t[11]=e("strong",null,"Current URL:",-1)),i(" "+o(s.window.location.href),1)]),e("div",null,[t[12]||(t[12]=e("strong",null,"Protocol:",-1)),i(" "+o(s.window.location.protocol),1)])])]),e("div",R,[t[15]||(t[15]=e("h3",{class:"font-medium mb-2"},"Network Test Results",-1)),e("div",z,[e("button",{onClick:b,disabled:n.value,class:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"}," Test Basic Connectivity ",8,U),e("button",{onClick:f,disabled:n.value,class:"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"}," Test CORS ",8,F),e("button",{onClick:y,disabled:n.value,class:"px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"}," Test Authentication ",8,K),e("button",{onClick:E,disabled:n.value,class:"px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"}," Test Orgs (Auth File) ",8,Y)]),m.value.length>0?(g(),u("div",L,[t[14]||(t[14]=e("h4",{class:"font-medium mb-2"},"Test Results:",-1)),e("div",H,[(g(!0),u(w,null,h(m.value,(l,c)=>(g(),u("div",{key:c,class:"text-sm mb-2"},[e("span",{class:V(l.success?"text-green-600":"text-red-600")},o(l.timestamp)+": "+o(l.message),3)]))),128))])])):_("",!0)])]),e("div",G,[t[23]||(t[23]=e("h3",{class:"font-medium text-blue-900 mb-2"},"Debugging Instructions",-1)),e("ol",j,[t[17]||(t[17]=e("li",null,"Open browser DevTools (F12 or right-click → Inspect)",-1)),t[18]||(t[18]=e("li",null,"Go to the Network tab in DevTools",-1)),t[19]||(t[19]=e("li",null,'Make sure "Preserve log" is checked',-1)),t[20]||(t[20]=e("li",null,"Click the test buttons above to trigger API calls",-1)),e("li",null,"Look for requests to "+o(a.VITE_BASE_API),1),t[21]||(t[21]=e("li",null,"Check the Console tab for detailed logs",-1)),t[22]||(t[22]=e("li",null,"If no network requests appear, there might be a JavaScript error preventing the calls",-1))])])])]))}});export{tt as default};
