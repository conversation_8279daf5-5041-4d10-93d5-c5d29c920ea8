import{d as Y,u as Z,r as b,p as ee,c as d,b as s,j as m,i as C,y as n,k as c,f as y,t as l,z as te,n as se,o as p}from"./index-DOaBqVmr.js";import{_ as oe}from"./DataTable.vue_vue_type_script_setup_true_lang-BF-LKR3y.js";import{_ as re}from"./FilterCards.vue_vue_type_script_setup_true_lang-Bk3N6i80.js";import{l as v}from"./loanApi-CajqAy4f.js";import{u as ae}from"./useBackofficeActions-O4W2PmYt.js";import{r as ne}from"./ArrowPathIcon-BCh5HUKO.js";import{r as le}from"./EllipsisVerticalIcon-BCCOPEML.js";import{r as ce}from"./ArrowDownTrayIcon-BWjpub36.js";const ue={class:"space-y-6"},ie={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},de={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},me={class:"mt-4 sm:mt-0 flex space-x-3"},pe={class:"flex space-x-2"},ge={class:"text-sm"},fe={class:"font-medium text-gray-900"},_e={class:"text-gray-500"},be={class:"text-xs text-gray-400"},ye={class:"text-sm"},ve={class:"font-medium text-gray-900"},xe={class:"text-xs text-gray-500"},he={class:"text-sm text-center"},we={class:"font-medium text-gray-900"},ke={class:"text-green-600"},qe={class:"text-sm text-gray-900"},Ce={class:"relative"},Re=["onClick"],Ae={key:0,class:"absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},$e={class:"py-1"},Fe=["onClick"],Se=["onClick"],De=["onClick"],Ne=["onClick"],je=["onClick"],Ue=Y({__name:"LoanRequests",setup(Le){const R=se(),h=Z(),{showDropdown:A,loading:r,currentPage:g,pageSize:w,totalRecords:f,searchQuery:$,toggleDropdown:F,goToPage:S,handleSearch:D}=ae(),_=b([]),N=b(""),j=b("asc"),u=b({client_id:h.selectedClientId||"",status:"",loan_number:"",reference_id:"",phone_number:"",start_date:"",end_date:"",amount:""}),L=[{value:"1",label:"Approved"},{value:"2",label:"Pending"},{value:"3",label:"Rejected"},{value:"4",label:"Awaiting TAN"},{value:"5",label:"Disbursed"},{value:"6",label:"Completed"}],T=[{key:"req_number",label:"Request No",sortable:!0},{key:"mobile",label:"Customer",sortable:!1},{key:"product_name",label:"Product",sortable:!0},{key:"amounts",label:"Amounts",sortable:!1},{key:"status",label:"Status",sortable:!0},{key:"created_at",label:"Date",sortable:!0}],a=async()=>{var t,o;r.value=!0;try{const e=await v.getLoanRequests({page:g.value,limit:w.value,search:$.value,...u.value});e.status===200?(_.value=((t=e.message)==null?void 0:t.data)||[],f.value=((o=e.message)==null?void 0:o.total_count)||0):(_.value=[],f.value=0)}catch(e){console.error("Error fetching loan requests:",e),_.value=[],f.value=0}finally{r.value=!1}},P=t=>{Object.assign(u.value,t)},E=t=>{S(t,a)},V=t=>{D(t,a)},z=(t,o)=>{N.value=t,j.value=o,a()},B=t=>{k(t)},M=()=>{a()},I=t=>{u.value={...t},g.value=1,a()},U=()=>{u.value={client_id:h.selectedClientId||"",status:"",loan_number:"",reference_id:"",phone_number:"",start_date:"",end_date:"",amount:""},g.value=1,a()},k=t=>{console.log("View details for request:",t.req_number)},K=async t=>{try{if(!confirm(`Are you sure you want to approve loan request ${t.req_number} for ${x(t.requested_amount)}?`))return;r.value=!0;const e=await v.approveLoanRequest(t.req_number,1,parseFloat(t.requested_amount.toString()),`Loan of ${t.requested_amount} approved`);e.status===200?(alert("Loan request approved successfully"),a()):alert(`Failed to approve loan request: ${e.message}`)}catch(o){console.error("Error approving loan request:",o),alert("Failed to approve loan request. Please try again.")}finally{r.value=!1}},O=async t=>{try{if(!confirm(`Are you sure you want to reject loan request ${t.req_number}?`))return;r.value=!0;const e=await v.approveLoanRequest(t.req_number,0,0,"Loan request rejected");e.status===200?(alert("Loan request rejected successfully"),a()):alert(`Failed to reject loan request: ${e.message}`)}catch(o){console.error("Error rejecting loan request:",o),alert("Failed to reject loan request. Please try again.")}finally{r.value=!1}},H=async t=>{try{if(!confirm(`Resend TAN code for loan request ${t.req_number}?`))return;r.value=!0;const e=await v.resendTAN(t.req_number);e.status===200?alert("TAN code sent successfully"):alert(`Failed to send TAN code: ${e.message}`)}catch(o){console.error("Error sending TAN:",o),alert("Failed to send TAN code. Please try again.")}finally{r.value=!1}},Q=t=>{R.push({name:"loan-repayments",query:{loan_number:t.req_number}})},G=()=>{console.log("Export loan requests data")},x=t=>{const o=typeof t=="string"?parseFloat(t):t;return new Intl.NumberFormat("en-KE",{style:"currency",currency:"KES",minimumFractionDigits:0,maximumFractionDigits:0}).format(o)},J=t=>new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),W=t=>({1:"Approved",2:"Pending",3:"Rejected",4:"Awaiting TAN",5:"Disbursed",6:"Completed"})[t]||"Unknown",X=t=>({1:"bg-green-100 text-green-800",2:"bg-yellow-100 text-yellow-800",3:"bg-red-100 text-red-800",4:"bg-purple-100 text-purple-800",5:"bg-blue-100 text-blue-800",6:"bg-gray-100 text-gray-800"})[t]||"bg-gray-100 text-gray-800";return ee(()=>{a()}),(t,o)=>(p(),d("div",ue,[s("div",ie,[s("div",de,[o[1]||(o[1]=s("div",null,[s("h1",{class:"text-2xl font-bold text-gray-900"},"Loan Requests"),s("p",{class:"text-gray-600 mt-1"},"Manage and review loan requests from customers")],-1)),s("div",me,[s("button",{onClick:M,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[m(n(ne),{class:"w-4 h-4 mr-2"}),o[0]||(o[0]=C(" Refresh "))])])])]),m(re,{filters:u.value,"filter-type":"requests","status-options":L,"onUpdate:filters":P,onApply:I,onClear:U},null,8,["filters"]),m(oe,{data:_.value,header:T,loading:n(r),"current-page":n(g),"total-records":n(f),"page-size":n(w),title:"Loan Requests","row-key":"req_number","has-actions":!0,onPageChange:E,onSearch:V,onSort:z,onRowClick:B},{"header-actions":c(()=>[s("div",pe,[s("button",{onClick:G,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[m(n(ce),{class:"w-4 h-4 mr-2"}),o[2]||(o[2]=C(" Export "))])])]),"cell-mobile":c(({item:e})=>[s("div",ge,[s("div",fe,"+"+l(e.mobile),1),s("div",_e,l(e.name),1),s("div",be,l(e.merchant_name),1)])]),"cell-product_name":c(({item:e})=>[s("div",ye,[s("div",ve,l(e.product_name),1),s("div",xe,l((parseFloat(e.interest_charged)*100).toFixed(1))+"% interest",1)])]),"cell-amounts":c(({item:e})=>[s("div",he,[s("div",we,"Req: "+l(x(e.requested_amount)),1),s("div",ke,"App: "+l(x(e.approved_amount)),1)])]),"cell-status":c(({item:e})=>[s("span",{class:te([X(e.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},l(W(e.status)),3)]),"cell-created_at":c(({item:e})=>[s("div",qe,l(J(e.created_at)),1)]),actions:c(({item:e,index:q})=>[s("div",Ce,[s("button",{onClick:i=>n(F)(q),class:"inline-flex items-center p-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[m(n(le),{class:"w-4 h-4"})],8,Re),n(A)[q]?(p(),d("div",Ae,[s("div",$e,[s("button",{onClick:i=>k(e),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," View Details ",8,Fe),e.status===2||e.status===5||e.status===4?(p(),d("button",{key:0,onClick:i=>K(e),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-green-50 hover:text-green-900"}," Approve ",8,Se)):y("",!0),e.status===2||e.status===5||e.status===4?(p(),d("button",{key:1,onClick:i=>O(e),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-900"}," Reject ",8,De)):y("",!0),e.status===4?(p(),d("button",{key:2,onClick:i=>H(e),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-900"}," Resend Code ",8,Ne)):y("",!0),s("button",{onClick:i=>Q(e),class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-900"}," View Repayments ",8,je)])])):y("",!0)])]),_:1},8,["data","loading","current-page","total-records","page-size"])]))}});export{Ue as default};
