import{c as a,b as e,o as i,d as A,r as b,j as g,i as f,y as c,g as E,f as S,h as d,v,C as x,F as y,m as w,t as n,z as V,n as B,B as N,E as U}from"./index-DOaBqVmr.js";import{P as D}from"./permissions-vZl7iLZK.js";import{s as T}from"./systemApi-CvVfCghC.js";import{r as j}from"./ArrowLeftIcon-DKfvydGS.js";import{r as I}from"./ShieldCheckIcon-ClBEPwdF.js";import{a as q,r as R}from"./PencilIcon-Bgul0WAp.js";import{r as F}from"./PlusIcon-BKTWa0k6.js";import{a as O,r as z}from"./TrashIcon-D374yQ2i.js";function H(h,u){return i(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m.75 12 3 3m0 0 3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])}const L={class:"space-y-6"},Q={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},Z={class:"flex items-center justify-between"},G={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},J={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},K=["value"],W={class:"md:col-span-2"},X={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Y=["onClick"],ee={class:"flex items-center"},se={class:"font-medium text-gray-900"},te={class:"text-sm text-gray-500 mt-1"},oe={class:"mt-2"},re={class:"text-xs bg-gray-100 px-2 py-1 rounded"},ne={key:0},ie={class:"bg-gray-50 rounded-lg p-4"},ae={class:"flex items-start space-x-4"},le={class:"flex-1"},de={class:"font-medium text-gray-900"},ue={class:"text-sm text-gray-600 mt-1"},me={class:"flex items-center mt-2 space-x-4"},ce={class:"text-xs text-gray-500"},pe={class:"font-medium"},be={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200"},ge=["disabled"],Pe=A({__name:"AddPermission",setup(h){const u=B(),l=b(!1),t=b({name:"",description:"",module:"",status:1}),k=Object.values(D),_=[{action:"view",label:"View",description:"Read-only access",pattern:"module.view",icon:q},{action:"create",label:"Create",description:"Create new records",pattern:"module.create",icon:F},{action:"edit",label:"Edit",description:"Modify existing records",pattern:"module.edit",icon:R},{action:"delete",label:"Delete",description:"Remove records",pattern:"module.delete",icon:O},{action:"approve",label:"Approve",description:"Approve requests",pattern:"module.approve",icon:z},{action:"export",label:"Export",description:"Export data",pattern:"module.export",icon:H}],C=r=>{t.value.module?(t.value.name=`${t.value.module}.${r.action}`,t.value.description=`${r.description} for ${m(t.value.module)}`):(t.value.name=r.pattern,t.value.description=r.description)},P=async()=>{if(!t.value.name.trim()){alert("Permission name is required");return}if(confirm("Are you sure you want to create this permission?")){l.value=!0;try{const r=await T.createPermission({name:t.value.name.trim(),description:t.value.description.trim(),module:t.value.module,status:t.value.status});r.status===200?(alert("Permission created successfully!"),u.push({name:"system-permissions"})):alert("Error creating permission: "+r.message)}catch(r){console.error("Error creating permission:",r),alert("Error creating permission")}finally{l.value=!1}}},p=()=>{u.push({name:"system-permissions"})},m=r=>r.charAt(0).toUpperCase()+r.slice(1).replace(/[_-]/g," "),M=r=>({1:"Active",0:"Inactive"})[r]||"Unknown",$=r=>({1:"bg-green-100 text-green-800",0:"bg-red-100 text-red-800"})[r]||"bg-gray-100 text-gray-800";return(r,s)=>(i(),a("div",L,[e("div",Q,[e("div",Z,[s[5]||(s[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Add New Permission"),e("p",{class:"text-gray-600 mt-1"},"Create a new system permission for access control")],-1)),e("button",{onClick:p,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[g(c(j),{class:"w-4 h-4 mr-2"}),s[4]||(s[4]=f(" Back to Permissions "))])])]),e("div",G,[e("form",{onSubmit:E(P,["prevent"]),class:"space-y-6"},[e("div",null,[s[13]||(s[13]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Basic Information",-1)),e("div",J,[e("div",null,[s[6]||(s[6]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Permission Name *",-1)),d(e("input",{"onUpdate:modelValue":s[0]||(s[0]=o=>t.value.name=o),type:"text",required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"e.g., users.view, loans.approve"},null,512),[[v,t.value.name]]),s[7]||(s[7]=e("p",{class:"mt-1 text-sm text-gray-500"},"Use dot notation for hierarchical permissions",-1))]),e("div",null,[s[9]||(s[9]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Module *",-1)),d(e("select",{"onUpdate:modelValue":s[1]||(s[1]=o=>t.value.module=o),required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},[s[8]||(s[8]=e("option",{value:""},"Select Module",-1)),(i(!0),a(y,null,w(c(k),o=>(i(),a("option",{key:o,value:o},n(m(o)),9,K))),128))],512),[[x,t.value.module]])]),e("div",W,[s[10]||(s[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Description",-1)),d(e("textarea",{"onUpdate:modelValue":s[2]||(s[2]=o=>t.value.description=o),rows:"3",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Describe what this permission allows users to do"},null,512),[[v,t.value.description]])]),e("div",null,[s[12]||(s[12]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Status",-1)),d(e("select",{"onUpdate:modelValue":s[3]||(s[3]=o=>t.value.status=o),class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},s[11]||(s[11]=[e("option",{value:1},"Active",-1),e("option",{value:0},"Inactive",-1)]),512),[[x,t.value.status]])])])]),e("div",null,[s[14]||(s[14]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Permission Templates",-1)),s[15]||(s[15]=e("p",{class:"text-sm text-gray-600 mb-4"},"Quick templates for common permission patterns",-1)),e("div",X,[(i(),a(y,null,w(_,o=>e("div",{key:o.action,onClick:fe=>C(o),class:"border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200"},[e("div",ee,[(i(),N(U(o.icon),{class:"w-5 h-5 text-blue-600 mr-2"})),e("h4",se,n(o.label),1)]),e("p",te,n(o.description),1),e("div",oe,[e("code",re,n(o.pattern),1)])],8,Y)),64))])]),t.value.name&&t.value.module?(i(),a("div",ne,[s[17]||(s[17]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Permission Preview",-1)),e("div",ie,[e("div",ae,[g(c(I),{class:"w-8 h-8 text-blue-600 mt-1"}),e("div",le,[e("h4",de,n(t.value.name),1),e("p",ue,n(t.value.description||"No description provided"),1),e("div",me,[e("span",ce,[s[16]||(s[16]=f("Module: ")),e("span",pe,n(m(t.value.module)),1)]),e("span",{class:V([$(t.value.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},n(M(t.value.status)),3)])])])])])):S("",!0),e("div",be,[e("button",{type:"button",onClick:p,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Cancel "),e("button",{type:"submit",disabled:l.value||!t.value.name||!t.value.module,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},n(l.value?"Creating...":"Create Permission"),9,ge)])],32)])]))}});export{Pe as default};
