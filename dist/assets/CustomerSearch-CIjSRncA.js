import{c as g,b as e,o as p,G as x,d as I,r as y,f as S,j as b,h as Z,y as v,v as G,H as J,B as q,i as N,t as m,k as j,F as L,m as K,E as F,z as T}from"./index-DOaBqVmr.js";import{_ as A,b as D,c as O,a as W,r as Y}from"./ActionButton.vue_vue_type_script_setup_true_lang-BwXxo59e.js";import{r as R}from"./MagnifyingGlassIcon-D7MUVaIi.js";import{r as Q}from"./XMarkIcon-CqcEu60T.js";import{r as ee}from"./CreditCardIcon-Bp2xwmwY.js";import"./TrashIcon-D374yQ2i.js";import"./UserGroupIcon-CvI7Mo90.js";import"./ArrowDownTrayIcon-BWjpub36.js";import"./PlusIcon-BKTWa0k6.js";import"./PencilIcon-Bgul0WAp.js";function H(n,d){return p(),g("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"})])}function _(n){const d=typeof n=="string"?n:JSON.stringify(n);let l=0;if(d.length===0)return l.toString();for(let i=0;i<d.length;i++){const a=d.charCodeAt(i);l=(l<<5)-l+a,l=l&l}return Math.abs(l).toString(36)}const P={async searchCustomer(n){var d,l,i;try{const a={loan_number:n.loan_number,limit:n.limit||10,offset:n.offset||1},t=_(a),r=(await x.post("merchant/v1/search/loan_accounts",a,{headers:{"X-Hash-Key":t}})).data.data;return{status:r.code,message:r.data||r,code:r.code.toString()}}catch(a){if(console.error("Error searching customer:",a),a.response){const t=a.response.status,o=a.response.data;return{status:((d=o.data)==null?void 0:d.code)||t,message:((l=o.data)==null?void 0:l.message)||"Failed to search customer",code:(((i=o.data)==null?void 0:i.code)||t).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async getLoanRequests(n){var d,l,i;try{const a={loan_number:n.loan_number,limit:n.limit||10,offset:n.offset||1,loan_request_number:n.loan_request_number||""},t=_(a),r=(await x.post("merchant/v1/view/loan_request",a,{headers:{"X-Hash-Key":t}})).data.data;return{status:r.code,message:r.data||r,code:r.code.toString()}}catch(a){if(console.error("Error fetching loan requests:",a),a.response){const t=a.response.status,o=a.response.data;return{status:((d=o.data)==null?void 0:d.code)||t,message:((l=o.data)==null?void 0:l.message)||"Failed to fetch loan requests",code:(((i=o.data)==null?void 0:i.code)||t).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async getLoanRepayments(n){var d,l,i;try{const a={loan_number:n.loan_number,limit:n.limit||10,offset:n.offset||1,request_number:n.request_number||""},t=_(a),r=(await x.post("merchant/v1/view/loan_repayments",a,{headers:{"X-Hash-Key":t}})).data.data;return{status:r.code,message:r.data||r,code:r.code.toString()}}catch(a){if(console.error("Error fetching loan repayments:",a),a.response){const t=a.response.status,o=a.response.data;return{status:((d=o.data)==null?void 0:d.code)||t,message:((l=o.data)==null?void 0:l.message)||"Failed to fetch loan repayments",code:(((i=o.data)==null?void 0:i.code)||t).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async getLimitRequests(n){var d,l,i;try{const a={loan_number:n.loan_number,limit:n.limit||10,offset:n.offset||1,reference_id:n.reference_id||""},t=_(a),r=(await x.post("merchant/v1/view/limit_requests",a,{headers:{"X-Hash-Key":t}})).data.data;return{status:r.code,message:r.data||r,code:r.code.toString()}}catch(a){if(console.error("Error fetching limit requests:",a),a.response){const t=a.response.status,o=a.response.data;return{status:((d=o.data)==null?void 0:d.code)||t,message:((l=o.data)==null?void 0:l.message)||"Failed to fetch limit requests",code:(((i=o.data)==null?void 0:i.code)||t).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async getTransactions(n){var d,l,i;try{const a={loan_number:n.loan_number,limit:n.limit||10,offset:n.offset||1,trxn_id:n.trxn_id||""},t=_(a),r=(await x.post("merchant/v1/view/transactions",a,{headers:{"X-Hash-Key":t}})).data.data;return{status:r.code,message:r.data||r,code:r.code.toString()}}catch(a){if(console.error("Error fetching transactions:",a),a.response){const t=a.response.status,o=a.response.data;return{status:((d=o.data)==null?void 0:d.code)||t,message:((l=o.data)==null?void 0:l.message)||"Failed to fetch transactions",code:(((i=o.data)==null?void 0:i.code)||t).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async toggleBulkStatus(n,d){var l,i,a;try{const t={loan_number:n,marketing_status:d?"1":"0"},o=_(t),h=(await x.post("merchant/v1/update/marketing_status",t,{headers:{"X-Hash-Key":o}})).data.data;return{status:h.code,message:h.message||h,code:h.code.toString()}}catch(t){if(console.error("Error toggling bulk status:",t),t.response){const o=t.response.status,r=t.response.data;return{status:((l=r.data)==null?void 0:l.code)||o,message:((i=r.data)==null?void 0:i.message)||"Failed to update bulk status",code:(((a=r.data)==null?void 0:a.code)||o).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async toggleBlockStatus(n,d,l){var i,a,t;try{const o={loan_number:n,block_status:d?"1":"0",reason:l||""},r=_(o),f=(await x.post("merchant/v1/update/block_status",o,{headers:{"X-Hash-Key":r}})).data.data;return{status:f.code,message:f.message||f,code:f.code.toString()}}catch(o){if(console.error("Error toggling block status:",o),o.response){const r=o.response.status,h=o.response.data;return{status:((i=h.data)==null?void 0:i.code)||r,message:((a=h.data)==null?void 0:a.message)||"Failed to update block status",code:(((t=h.data)==null?void 0:t.code)||r).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}}},te={class:"space-y-6"},se={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},ae={class:"flex items-center space-x-4"},re={class:"flex-1"},oe={class:"relative"},ne={class:"flex-shrink-0"},le=["disabled"],de={key:1,class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"},ie={key:0,class:"flex justify-center items-center py-12"},ce={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4"},ue={class:"flex"},me={class:"flex-shrink-0"},pe={class:"ml-3"},ge={class:"mt-2 text-sm text-red-700"},he={key:2,class:"space-y-6"},ye={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},fe={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},xe={class:"text-center"},be={class:"mx-auto h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center mb-4"},_e={class:"text-2xl font-bold text-gray-900 mb-2"},ve={class:"space-y-1 text-gray-600"},we={class:"text-lg"},ke={class:"font-medium text-blue-600"},Se={class:"mt-6 flex justify-center space-x-3"},Ne={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},Ce={class:"space-y-4"},qe={class:"flex justify-between"},De={class:"text-gray-900 font-semibold"},Ee={class:"flex justify-between"},Be={class:"text-gray-900 font-semibold"},je={class:"flex justify-between"},Le={class:"text-gray-900"},Ke={class:"flex justify-between"},Fe={class:"text-gray-900"},Te={class:"mt-6 pt-4 border-t border-gray-200"},Ae={class:"space-y-3"},Re={class:"flex justify-between"},He={class:"text-gray-900 font-semibold"},Pe={class:"flex justify-between"},$e={class:"text-gray-900 font-semibold"},Xe={class:"bg-white rounded-xl shadow-sm border border-gray-200"},Me={class:"border-b border-gray-200"},Ue={class:"-mb-px flex space-x-8 px-6","aria-label":"Tabs"},Ve=["onClick"],ze={key:0,class:"ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium"},Ie={class:"p-6"},Ze={key:0},Ge={key:0,class:"flex justify-center py-8"},Je={key:1,class:"text-center py-8 text-gray-500"},Oe={key:2,class:"overflow-x-auto"},We={class:"min-w-full divide-y divide-gray-200"},Ye={class:"bg-white divide-y divide-gray-200"},Qe={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},et={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},tt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},st={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},at={class:"px-6 py-4 whitespace-nowrap"},rt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},ot={key:1,class:"text-center py-12 text-gray-500"},nt={key:3,class:"text-center py-12"},ft=I({__name:"CustomerSearch",setup(n){const d=y(!1),l=y(!1),i=y(""),a=y(""),t=y(null),o=y(!1),r=y("loan_requests"),h=y([]),f=[{id:"loan_requests",name:"Loan Requests",icon:D,count:0},{id:"repayments",name:"Repayments",icon:ee,count:0},{id:"limits",name:"Limits",icon:O,count:0},{id:"transactions",name:"Transactions",icon:W,count:0},{id:"kyc",name:"KYC",icon:Y,count:0}],E=async()=>{var c,s;if(a.value.trim()){d.value=!0,i.value="",o.value=!0;try{let u=a.value.trim();u.startsWith("0")&&(u=u.substring(1));const k=await P.searchCustomer({loan_number:u});k.status===200&&((s=(c=k.message)==null?void 0:c.data)==null?void 0:s.length)>0?(t.value=k.message.data[0],await B()):(t.value=null,i.value="No customer found with this phone number")}catch(u){i.value=u.message||"An error occurred while searching",t.value=null}finally{d.value=!1}}},$=async c=>{switch(r.value=c,c){case"loan_requests":await B();break}},B=async()=>{var c;if(t.value){l.value=!0;try{const s=await P.getLoanRequests({loan_number:t.value.loan_number,limit:10,offset:1});s.status===200&&(h.value=((c=s.message)==null?void 0:c.data)||[])}catch(s){console.error("Error fetching loan requests:",s)}finally{l.value=!1}}},X=async()=>{var c;console.log("Toggle bulk status for:",(c=t.value)==null?void 0:c.name)},M=async()=>{var c;console.log("Toggle block for:",(c=t.value)==null?void 0:c.name)},U=()=>{const c=f.find(s=>s.id===r.value);return(c==null?void 0:c.icon)||D},V=c=>{switch(parseInt(c)){case 1:return"bg-green-100 text-green-800";case 2:return"bg-orange-100 text-orange-800";case 3:return"bg-red-100 text-red-800";case 4:return"bg-purple-100 text-purple-800";case 5:return"bg-blue-100 text-blue-800";case 6:return"bg-yellow-100 text-yellow-800";case 7:return"bg-red-100 text-red-800";case 8:return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},z=c=>{switch(parseInt(c)){case 1:return"Fully Paid";case 2:return"Partially Paid";case 3:return"Rejected";case 4:return"Unverified";case 5:return"Pending";case 6:return"Unpaid";case 7:return"Failure";case 8:return"Approved";default:return"Unknown"}},w=c=>{const s=typeof c=="string"?parseFloat(c):c;return new Intl.NumberFormat("en-KE",{style:"currency",currency:"KES"}).format(s||0)},C=c=>c?new Date(c).toLocaleDateString("en-KE",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"N/A";return(c,s)=>(p(),g("div",te,[s[19]||(s[19]=e("div",{class:"flex items-center justify-between"},[e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Customer Search"),e("p",{class:"mt-1 text-sm text-gray-500"}," Search for customers by phone number and view their details ")])],-1)),e("div",se,[e("div",ae,[e("div",re,[s[1]||(s[1]=e("label",{for:"phone",class:"block text-sm font-medium text-gray-700 mb-2"}," Phone Number ",-1)),e("div",oe,[b(v(R),{class:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),Z(e("input",{id:"phone","onUpdate:modelValue":s[0]||(s[0]=u=>a.value=u),type:"tel",placeholder:"Enter phone number to search (e.g., 0714919776)",class:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",onKeyup:J(E,["enter"])},null,544),[[G,a.value]])])]),e("div",ne,[e("button",{onClick:E,disabled:d.value||!a.value.trim(),class:"inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"},[d.value?(p(),g("div",de)):(p(),q(v(R),{key:0,class:"h-4 w-4 mr-2"})),N(" "+m(d.value?"Searching...":"Search"),1)],8,le)])])]),d.value?(p(),g("div",ie,s[2]||(s[2]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"},null,-1)]))):i.value?(p(),g("div",ce,[e("div",ue,[e("div",me,[b(v(Q),{class:"h-5 w-5 text-red-400"})]),e("div",pe,[s[3]||(s[3]=e("h3",{class:"text-sm font-medium text-red-800"},"Error",-1)),e("div",ge,[e("p",null,m(i.value),1)])])])])):t.value?(p(),g("div",he,[e("div",ye,[e("div",fe,[e("div",xe,[e("div",be,[b(v(H),{class:"h-10 w-10 text-gray-400"})]),e("h3",_e,m(t.value.name),1),e("div",ve,[e("p",we,"+"+m(t.value.msisdn),1),e("p",null,m(t.value.loan_number),1),e("p",null,m(t.value.employee_number),1),e("p",ke,m(t.value.client_name),1)])]),e("div",Se,[b(A,{variant:t.value.marketing_status==="1"?"warning":"success",size:"sm",shape:"rounded",onClick:X},{default:j(()=>[N(m(t.value.marketing_status==="1"?"Disable Bulk":"Enable Bulk"),1)]),_:1},8,["variant"]),b(A,{variant:"danger",size:"sm",shape:"rounded",onClick:M},{default:j(()=>s[4]||(s[4]=[N(" Block ")])),_:1,__:[4]})])]),e("div",Ne,[s[12]||(s[12]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Customer Details",-1)),e("div",Ce,[e("div",qe,[s[5]||(s[5]=e("span",{class:"font-medium text-gray-700"},"Max Approved Loan",-1)),e("span",De,m(w(t.value.max_approved)),1)]),e("div",Ee,[s[6]||(s[6]=e("span",{class:"font-medium text-gray-700"},"Credit Score",-1)),e("span",Be,m(t.value.credit_score),1)]),e("div",je,[s[7]||(s[7]=e("span",{class:"font-medium text-gray-700"},"Employment Date",-1)),e("span",Le,m(C(t.value.employment_date)),1)]),e("div",Ke,[s[8]||(s[8]=e("span",{class:"font-medium text-gray-700"},"Last Login",-1)),e("span",Fe,m(t.value.last_login_date?C(t.value.last_login_date):"Never"),1)])]),e("div",Te,[s[11]||(s[11]=e("h4",{class:"font-medium text-gray-900 mb-3 text-center"},"Account Balances",-1)),e("div",Ae,[e("div",Re,[s[9]||(s[9]=e("span",{class:"font-medium text-gray-700"},"Actual Balance",-1)),e("span",He,m(w(t.value.actual_balance)),1)]),e("div",Pe,[s[10]||(s[10]=e("span",{class:"font-medium text-gray-700"},"Loan Balance",-1)),e("span",$e,m(w(t.value.loan_balance)),1)])])])])]),e("div",Xe,[e("div",Me,[e("nav",Ue,[(p(),g(L,null,K(f,u=>e("button",{key:u.id,onClick:k=>$(u.id),class:T([r.value===u.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300","whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center"])},[(p(),q(F(u.icon),{class:"h-5 w-5 mr-2"})),N(" "+m(u.name)+" ",1),u.count!==void 0?(p(),g("span",ze,m(u.count),1)):S("",!0)],10,Ve)),64))])]),e("div",Ie,[r.value==="loan_requests"?(p(),g("div",Ze,[s[16]||(s[16]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Loan Requests")],-1)),l.value?(p(),g("div",Ge,s[13]||(s[13]=[e("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"},null,-1)]))):h.value.length===0?(p(),g("div",Je,[b(v(D),{class:"h-12 w-12 mx-auto mb-4 text-gray-300"}),s[14]||(s[14]=e("p",null,"No loan requests found for this customer.",-1))])):(p(),g("div",Oe,[e("table",We,[s[15]||(s[15]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Request No"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Product"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Requested"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Approved"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Date")])],-1)),e("tbody",Ye,[(p(!0),g(L,null,K(h.value,u=>(p(),g("tr",{key:u.req_number},[e("td",Qe,m(u.req_number),1),e("td",et,m(u.product_name||"N/A"),1),e("td",tt,m(w(u.requested_amount)),1),e("td",st,m(w(u.approved_amount)),1),e("td",at,[e("span",{class:T([V(u.approval_status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},m(z(u.approval_status)),3)]),e("td",rt,m(C(u.created)),1)]))),128))])])]))])):S("",!0),r.value!=="loan_requests"?(p(),g("div",ot,[(p(),q(F(U()),{class:"h-12 w-12 mx-auto mb-4 text-gray-300"})),e("p",null,m(r.value.replace("_"," ").replace(/\b\w/g,u=>u.toUpperCase()))+" data will be displayed here.",1)])):S("",!0)])])])):o.value&&!t.value?(p(),g("div",nt,[b(v(H),{class:"mx-auto h-12 w-12 text-gray-400 mb-4"}),s[17]||(s[17]=e("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No customer found",-1)),s[18]||(s[18]=e("p",{class:"text-gray-500"},"Try searching with a different phone number.",-1))])):S("",!0)]))}});export{ft as default};
