import{c as n,b as t,o as l,d as _,u as w,x as u,r as k,e as S,t as d,j as s,y as o,k as i,l as T,F as B,m as V,i as D,z as C}from"./index-DOaBqVmr.js";import{r as x}from"./BuildingOfficeIcon-Cd0d7gpT.js";import{r as p}from"./CurrencyDollarIcon-C8tInHXu.js";import{r as b}from"./CreditCardIcon-Bp2xwmwY.js";import{r as M}from"./MagnifyingGlassIcon-D7MUVaIi.js";function v(h,m){return l(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"})])}const L={class:"space-y-6"},j={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},N={class:"flex items-center justify-between"},R={class:"text-2xl font-bold text-gray-900"},$={class:"hidden md:block"},E={class:"bg-blue-50 rounded-lg p-4"},P={class:"text-blue-900 text-lg font-semibold"},z={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},A={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},F={class:"flex items-center"},W={class:"p-3 rounded-lg bg-blue-100"},q={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},O={class:"flex items-center"},U={class:"p-3 rounded-lg bg-green-100"},G={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},H={class:"flex items-center"},I={class:"p-3 rounded-lg bg-purple-100"},Z={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},Q={class:"flex items-center"},J={class:"p-3 rounded-lg bg-orange-100"},K={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},X={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},Y={class:"flex items-center justify-between mb-4"},tt={class:"space-y-4"},et={class:"flex items-center"},st={class:"w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center"},ot={class:"ml-3"},rt={class:"text-sm font-medium text-gray-900"},dt={class:"text-xs text-gray-500"},at={class:"text-right"},it={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},nt={class:"grid grid-cols-2 gap-4"},xt=_({__name:"Dashboard",setup(h){const m=w(),c=u(()=>{var g,e;return((g=m.user)==null?void 0:g.name)||((e=m.user)==null?void 0:e.username)||null}),y=u(()=>new Date().toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})),f=k([{id:1,description:"Loan Payment Received",amount:2500,time:"2 minutes ago"},{id:2,description:"Withdrawal Request",amount:-1200,time:"15 minutes ago"},{id:3,description:"Bill Payment",amount:-350,time:"1 hour ago"},{id:4,description:"Deposit",amount:5e3,time:"2 hours ago"},{id:5,description:"Transfer",amount:-800,time:"3 hours ago"}]);return(g,e)=>{const a=T("router-link");return l(),n("div",L,[t("div",j,[t("div",N,[t("div",null,[t("h2",R,"Welcome back"+d(c.value?`, ${c.value}`:"")+"!",1),e[0]||(e[0]=t("p",{class:"text-gray-600 mt-1"},"Here's what's happening with your financial platform today.",-1))]),t("div",$,[t("div",E,[e[1]||(e[1]=t("div",{class:"text-blue-600 text-sm font-medium"},"Today's Date",-1)),t("div",P,d(y.value),1)])])])]),t("div",z,[t("div",A,[t("div",F,[t("div",W,[s(o(x),{class:"w-6 h-6 text-blue-600"})]),e[2]||(e[2]=t("div",{class:"ml-4"},[t("p",{class:"text-sm font-medium text-gray-600"},"Total Organizations"),t("p",{class:"text-2xl font-semibold text-gray-900"},"1,234")],-1))]),e[3]||(e[3]=t("div",{class:"mt-4"},[t("span",{class:"text-green-600 text-sm font-medium"},"+12%"),t("span",{class:"text-gray-600 text-sm ml-1"},"from last month")],-1))]),t("div",q,[t("div",O,[t("div",U,[s(o(p),{class:"w-6 h-6 text-green-600"})]),e[4]||(e[4]=t("div",{class:"ml-4"},[t("p",{class:"text-sm font-medium text-gray-600"},"Active Loans"),t("p",{class:"text-2xl font-semibold text-gray-900"},"5,678")],-1))]),e[5]||(e[5]=t("div",{class:"mt-4"},[t("span",{class:"text-green-600 text-sm font-medium"},"+8%"),t("span",{class:"text-gray-600 text-sm ml-1"},"from last month")],-1))]),t("div",G,[t("div",H,[t("div",I,[s(o(b),{class:"w-6 h-6 text-purple-600"})]),e[6]||(e[6]=t("div",{class:"ml-4"},[t("p",{class:"text-sm font-medium text-gray-600"},"Transactions Today"),t("p",{class:"text-2xl font-semibold text-gray-900"},"892")],-1))]),e[7]||(e[7]=t("div",{class:"mt-4"},[t("span",{class:"text-green-600 text-sm font-medium"},"+15%"),t("span",{class:"text-gray-600 text-sm ml-1"},"from yesterday")],-1))]),t("div",Z,[t("div",Q,[t("div",J,[s(o(v),{class:"w-6 h-6 text-orange-600"})]),e[8]||(e[8]=t("div",{class:"ml-4"},[t("p",{class:"text-sm font-medium text-gray-600"},"System Users"),t("p",{class:"text-2xl font-semibold text-gray-900"},"156")],-1))]),e[9]||(e[9]=t("div",{class:"mt-4"},[t("span",{class:"text-green-600 text-sm font-medium"},"+3%"),t("span",{class:"text-gray-600 text-sm ml-1"},"from last month")],-1))])]),t("div",K,[t("div",X,[t("div",Y,[e[11]||(e[11]=t("h3",{class:"text-lg font-semibold text-gray-900"},"Recent Transactions",-1)),s(a,{to:{name:"transactions"},class:"text-blue-600 hover:text-blue-700 text-sm font-medium"},{default:i(()=>e[10]||(e[10]=[D(" View all ")])),_:1,__:[10]})]),t("div",tt,[(l(!0),n(B,null,V(f.value,r=>(l(),n("div",{key:r.id,class:"flex items-center justify-between"},[t("div",et,[t("div",st,[s(o(b),{class:"w-5 h-5 text-gray-600"})]),t("div",ot,[t("p",rt,d(r.description),1),t("p",dt,d(r.time),1)])]),t("div",at,[t("p",{class:C(["text-sm font-medium",r.amount>0?"text-green-600":"text-red-600"])},d(r.amount>0?"+":"")+"$"+d(Math.abs(r.amount).toLocaleString()),3)])]))),128))])]),t("div",it,[e[16]||(e[16]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Quick Actions",-1)),t("div",nt,[s(a,{to:{name:"organisations"},class:"p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors duration-200"},{default:i(()=>[s(o(x),{class:"w-8 h-8 text-blue-600 mb-2"}),e[12]||(e[12]=t("p",{class:"text-sm font-medium text-gray-900"},"Manage Organizations",-1))]),_:1,__:[12]}),s(a,{to:{name:"requests"},class:"p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors duration-200"},{default:i(()=>[s(o(p),{class:"w-8 h-8 text-green-600 mb-2"}),e[13]||(e[13]=t("p",{class:"text-sm font-medium text-gray-900"},"Loan Requests",-1))]),_:1,__:[13]}),s(a,{to:{name:"customers"},class:"p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors duration-200"},{default:i(()=>[s(o(M),{class:"w-8 h-8 text-purple-600 mb-2"}),e[14]||(e[14]=t("p",{class:"text-sm font-medium text-gray-900"},"Search Customers",-1))]),_:1,__:[14]}),s(a,{to:{name:"system-roles"},class:"p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors duration-200"},{default:i(()=>[s(o(v),{class:"w-8 h-8 text-orange-600 mb-2"}),e[15]||(e[15]=t("p",{class:"text-sm font-medium text-gray-900"},"System Roles",-1))]),_:1,__:[15]})])])]),e[17]||(e[17]=S('<div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border-2 border-green-200 p-6"><h3 class="text-lg font-semibold text-green-800 mb-2">🎉 New Layout Working!</h3><p class="text-green-700 mb-4"> This is the new Vue 3 + Vite + Pinia dashboard with modern layout. The sidebar is on the left, this content is on the right - perfectly positioned side by side! </p><div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm"><div class="bg-white rounded-lg p-3"><strong class="text-gray-900">Framework:</strong><p class="text-gray-600">Vue 3 + TypeScript</p></div><div class="bg-white rounded-lg p-3"><strong class="text-gray-900">Build Tool:</strong><p class="text-gray-600">Vite (Fast &amp; Modern)</p></div><div class="bg-white rounded-lg p-3"><strong class="text-gray-900">State Management:</strong><p class="text-gray-600">Pinia (Vue 3 Official)</p></div></div></div><div class="bg-red-500 text-white p-4 rounded-lg"><h3 class="text-xl font-bold">🔴 CSS DEBUG TEST</h3><p class="mt-2">If this box is RED with WHITE text, Tailwind CSS is working!</p><div class="mt-4 flex space-x-4"><div class="bg-blue-600 px-4 py-2 rounded">Blue Button</div><div class="bg-green-600 px-4 py-2 rounded">Green Button</div><div class="bg-purple-600 px-4 py-2 rounded">Purple Button</div></div></div>',2))])}}});export{xt as default};
