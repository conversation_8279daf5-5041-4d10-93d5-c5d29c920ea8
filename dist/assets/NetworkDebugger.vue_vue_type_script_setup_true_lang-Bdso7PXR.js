import{o as C}from"./organizationsApi-CM42ShLT.js";import{c as I}from"./clientsApi-DPSRLazl.js";import{m as _}from"./merchantsApi-DyJPmv37.js";import{d as k,r as u,x as d,p as P,L as w,c as b,b as e,f as O,z,t as a,y as i,s as T,o as m}from"./index-DOaBqVmr.js";const E={class:"bg-gray-100 p-4 rounded-lg border"},M={class:"mb-4"},N={class:"flex items-center space-x-2"},D={class:"text-sm text-gray-500"},S={class:"mb-4"},F={class:"space-x-2"},R=["disabled"],B=["disabled"],L=["disabled"],J=["disabled"],$={key:0,class:"mb-4"},U={class:"mb-4"},V={class:"bg-white p-3 rounded border max-h-40 overflow-y-auto"},q={class:"text-xs"},G={class:"mb-4"},H={class:"text-sm space-y-1"},j={class:"text-sm text-gray-600"},Z=k({__name:"NetworkDebugger",setup(K){const o=u(!1),l=u(""),n=u(!1),r=w(),c=!1,g=d(()=>!!localStorage.getItem("token")),p=d(()=>!!localStorage.getItem("selectedClientId")),v=d(()=>n.value?{color:"bg-green-500",text:"Connected"}:{color:"bg-red-500",text:"Disconnected"}),f=async()=>{o.value=!0,console.log("🧪 Testing Organizations API...");try{const s=await C.getOrganizations({limit:5});l.value=JSON.stringify(s,null,2),console.log("✅ Organizations API Response:",s),n.value=!0}catch(s){l.value=`Error: ${s}`,console.error("❌ Organizations API Error:",s),n.value=!1}finally{o.value=!1}},h=async()=>{o.value=!0,console.log("🧪 Testing Clients API...");try{const s=await I.getClients({limit:5});l.value=JSON.stringify(s,null,2),console.log("✅ Clients API Response:",s),n.value=!0}catch(s){l.value=`Error: ${s}`,console.error("❌ Clients API Error:",s),n.value=!1}finally{o.value=!1}},y=async()=>{o.value=!0,console.log("🧪 Testing Merchants API...");try{const s=await _.getMerchants({limit:5});l.value=JSON.stringify(s,null,2),console.log("✅ Merchants API Response:",s),n.value=!0}catch(s){l.value=`Error: ${s}`,console.error("❌ Merchants API Error:",s),n.value=!1}finally{o.value=!1}},x=async()=>{o.value=!0,console.log("🧪 Testing Organizations API FROM AUTH FILE...");try{const s=await T.getOrganizationsDebug({limit:5});l.value=JSON.stringify(s,null,2),console.log("✅ Organizations API (Auth File) Response:",s),n.value=!0}catch(s){l.value=`Error: ${s}`,console.error("❌ Organizations API (Auth File) Error:",s),n.value=!1}finally{o.value=!1}},A=()=>{console.clear(),l.value="",console.log("🧹 Console cleared")};return P(()=>{console.log("🔧 Network Debugger mounted"),console.log("Environment:",{isDev:c,apiBaseUrl:r,hasToken:g.value,hasClientId:p.value})}),(s,t)=>(m(),b("div",E,[t[10]||(t[10]=e("h3",{class:"text-lg font-semibold mb-4"},"Network Debugger",-1)),e("div",M,[t[0]||(t[0]=e("h4",{class:"font-medium mb-2"},"API Status",-1)),e("div",N,[e("div",{class:z([v.value.color,"w-3 h-3 rounded-full"])},null,2),e("span",null,a(v.value.text),1),e("span",D,"("+a(i(r))+")",1)])]),e("div",S,[t[1]||(t[1]=e("h4",{class:"font-medium mb-2"},"Test API Calls",-1)),e("div",F,[e("button",{onClick:f,disabled:o.value,class:"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"}," Test Organizations ",8,R),e("button",{onClick:h,disabled:o.value,class:"px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"}," Test Clients ",8,B),e("button",{onClick:y,disabled:o.value,class:"px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"}," Test Merchants ",8,L),e("button",{onClick:x,disabled:o.value,class:"px-3 py-1 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"}," Test Orgs (Auth File) ",8,J),e("button",{onClick:A,class:"px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"}," Clear Logs ")])]),o.value?(m(),b("div",$,t[2]||(t[2]=[e("div",{class:"flex items-center space-x-2"},[e("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"}),e("span",null,"Making API call...")],-1)]))):O("",!0),e("div",U,[t[3]||(t[3]=e("h4",{class:"font-medium mb-2"},"Last Response",-1)),e("div",V,[e("pre",q,a(l.value||"No response yet"),1)])]),e("div",G,[t[4]||(t[4]=e("h4",{class:"font-medium mb-2"},"Environment Info",-1)),e("div",H,[e("div",null,"Mode: "+a(i(c)?"Development":"Production"),1),e("div",null,"Base API: "+a(i(r)),1),e("div",null,"Token: "+a(g.value?"Present":"Missing"),1),e("div",null,"Client ID: "+a(p.value?"Present":"Missing"),1)])]),e("div",j,[t[5]||(t[5]=e("p",null,[e("strong",null,"Instructions:")],-1)),t[6]||(t[6]=e("p",null,"1. Open browser DevTools (F12)",-1)),t[7]||(t[7]=e("p",null,"2. Go to Network tab",-1)),t[8]||(t[8]=e("p",null,"3. Click test buttons above",-1)),t[9]||(t[9]=e("p",null,"4. Check Console tab for detailed logs",-1)),e("p",null,"5. Look for requests to "+a(i(r)),1)])]))}});export{Z as _};
