import{K as d,G as i}from"./index-DOaBqVmr.js";const g={async getClients(s={}){var n,c;try{const r={limit:s.limit||10,offset:s.offset||0,page:s.page||1,status:"1",...s},e=d(r),t=(await i.post("merchant/v1/view/companies",r,{headers:{"X-Hash-Key":e}})).data.data,o=((n=t.data)==null?void 0:n.data)||t.data||[],l=((c=t.data)==null?void 0:c.total_count)||o.length||0,u={data:o,total_count:parseInt(l.toString())||0,current_page:t.current_page||1,per_page:t.per_page||10,last_page:t.last_page||1,from:t.from||0,to:t.to||0};return{status:t.code===200?200:t.code,message:u,code:t.code.toString()}}catch(r){if(console.error("Clients API error:",r),r.response){const e=r.response.status,a=r.response.data;if(e===422||e===500)return{status:e,message:{data:[],total_count:0,current_page:1,per_page:10},code:e.toString()};if(a.data)return{status:a.data.code||e,message:{data:[],total_count:0,current_page:1,per_page:10},code:(a.data.code||e).toString()}}return{status:500,message:{data:[],total_count:0,current_page:1,per_page:10},code:"500"}}},async getClient(s){var n,c,r;try{const e={client_id:s},a=d(e),o=(await i.post("merchant/v1/view/client",e,{headers:{"X-Hash-Key":a}})).data.data;return{status:o.code===200?200:o.code,message:o.data||{},code:o.code.toString()}}catch(e){if(console.error("Error fetching client:",e),e.response){const a=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||a,message:((c=t.data)==null?void 0:c.message)||"Failed to fetch client",code:(((r=t.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:{},code:"500"}}},async addClient(s){var n,c,r;try{const e={client_name:s.client_name,client_email:s.client_email,client_phone:s.client_phone,client_address:s.client_address,can_issue_loans:s.can_issue_loans,service_fee:s.service_fee,currency_code:s.currency_code,open_date:s.open_date,close_date:s.close_date,b2c_paybill:s.b2c_paybill,c2b_paybill:s.c2b_paybill,sender_id:s.sender_id},a=d(e),o=(await i.post("merchant/v1/create_client",e,{headers:{"X-Hash-Key":a}})).data.data;return{status:o.code,message:o.message||o,code:o.code.toString()}}catch(e){if(console.error("Error adding client:",e),e.response){const a=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||a,message:((c=t.data)==null?void 0:c.message)||"Failed to add client",code:(((r=t.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async updateClient(s){var n,c,r;try{const e={client_id:s.client_id,client_name:s.client_name,client_email:s.client_email,client_phone:s.client_phone,client_address:s.client_address,can_issue_loans:s.can_issue_loans,service_fee:s.service_fee,currency_code:s.currency_code,open_date:s.open_date,close_date:s.close_date,b2c_paybill:s.b2c_paybill,c2b_paybill:s.c2b_paybill,sender_id:s.sender_id},a=d(e),o=(await i.post("merchant/v1/update_client",e,{headers:{"X-Hash-Key":a}})).data.data;return{status:o.code,message:o.message||o,code:o.code.toString()}}catch(e){if(console.error("Error updating client:",e),e.response){const a=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||a,message:((c=t.data)==null?void 0:c.message)||"Failed to update client",code:(((r=t.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async activateClient(s){var n,c,r;try{const e={client_id:s,action:"activate"},a=d(e),o=(await i.post("merchant/v1/client_status",e,{headers:{"X-Hash-Key":a}})).data.data;return{status:o.code,message:o.message||o,code:o.code.toString()}}catch(e){if(console.error("Error activating client:",e),e.response){const a=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||a,message:((c=t.data)==null?void 0:c.message)||"Failed to activate client",code:(((r=t.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async deactivateClient(s){var n,c,r;try{const e={client_id:s,action:"deactivate"},a=d(e),o=(await i.post("merchant/v1/client_status",e,{headers:{"X-Hash-Key":a}})).data.data;return{status:o.code,message:o.message||o,code:o.code.toString()}}catch(e){if(console.error("Error deactivating client:",e),e.response){const a=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||a,message:((c=t.data)==null?void 0:c.message)||"Failed to deactivate client",code:(((r=t.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async deleteClient(s){var n,c,r;try{const e={client_id:s},a=d(e),o=(await i.post("merchant/v1/delete_client",e,{headers:{"X-Hash-Key":a}})).data.data;return{status:o.code,message:o.message||o,code:o.code.toString()}}catch(e){if(console.error("Error deleting client:",e),e.response){const a=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||a,message:((c=t.data)==null?void 0:c.message)||"Failed to delete client",code:(((r=t.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async getClientForEdit(s){var n,c,r;try{const e={client_id:s},a=d(e),o=(await i.post("merchant/v1/view/client_details",e,{headers:{"X-Hash-Key":a}})).data.data;return{status:o.code===200?200:o.code,message:o.data||{},code:o.code.toString()}}catch(e){if(console.error("Error fetching client for edit:",e),e.response){const a=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||a,message:((c=t.data)==null?void 0:c.message)||"Failed to fetch client details",code:(((r=t.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:{},code:"500"}}},async getClientLoanAccounts(s){var n,c,r;try{const e={client_id:s.client_id,limit:s.limit||10,offset:s.offset||1,loan_number:s.loan_number||""},a=d(e),o=(await i.post("merchant/v1/view/loan_accounts",e,{headers:{"X-Hash-Key":a}})).data.data;return{status:o.code,message:o.data||o,code:o.code.toString()}}catch(e){if(console.error("Error fetching client loan accounts:",e),e.response){const a=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||a,message:((c=t.data)==null?void 0:c.message)||"Failed to fetch loan accounts",code:(((r=t.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async getClientLoanRequests(s){var n,c,r;try{const e={client_id:s.client_id,limit:s.limit||10,offset:s.offset||1,loan_request_number:s.loan_request_number||"",status:s.status||""},a=d(e),o=(await i.post("merchant/v1/view/loan_request",e,{headers:{"X-Hash-Key":a}})).data.data;return{status:o.code,message:o.data||o,code:o.code.toString()}}catch(e){if(console.error("Error fetching client loan requests:",e),e.response){const a=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||a,message:((c=t.data)==null?void 0:c.message)||"Failed to fetch loan requests",code:(((r=t.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async getClientLoanRepayments(s){var n,c,r;try{const e={client_id:s.client_id,limit:s.limit||10,offset:s.offset||1,request_number:s.request_number||""},a=d(e),o=(await i.post("merchant/v1/view/loan_repayments",e,{headers:{"X-Hash-Key":a}})).data.data;return{status:o.code,message:o.data||o,code:o.code.toString()}}catch(e){if(console.error("Error fetching client loan repayments:",e),e.response){const a=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||a,message:((c=t.data)==null?void 0:c.message)||"Failed to fetch loan repayments",code:(((r=t.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}},async getClientLimitRequests(s){var n,c,r;try{const e={client_id:s.client_id,limit:s.limit||10,offset:s.offset||1,reference_id:s.reference_id||""},a=d(e),o=(await i.post("merchant/v1/view/limit_requests",e,{headers:{"X-Hash-Key":a}})).data.data;return{status:o.code,message:o.data||o,code:o.code.toString()}}catch(e){if(console.error("Error fetching client limit requests:",e),e.response){const a=e.response.status,t=e.response.data;return{status:((n=t.data)==null?void 0:n.code)||a,message:((c=t.data)==null?void 0:c.message)||"Failed to fetch limit requests",code:(((r=t.data)==null?void 0:r.code)||a).toString()}}return{status:500,message:"Network error or server unavailable",code:"500"}}}};export{g as c};
