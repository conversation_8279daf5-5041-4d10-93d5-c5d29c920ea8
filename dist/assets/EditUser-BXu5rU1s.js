import{d as L,r as d,x as S,p as O,c as n,b as s,j as T,i as z,y as G,h as m,v as _,C as k,f as H,F as v,m as y,t as c,g as J,q as K,n as Q,D as W,o as a}from"./index-DOaBqVmr.js";import{s as x}from"./systemApi-CvVfCghC.js";import{c as X}from"./clientsApi-DPSRLazl.js";import{r as Y}from"./ArrowLeftIcon-DKfvydGS.js";const Z={class:"space-y-6"},ee={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},se={class:"flex items-center justify-between"},te={key:0,class:"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center"},oe={key:1,class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},re={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},le=["value"],ne={key:0,class:"mt-1 text-sm text-gray-500"},ae={class:"bg-gray-50 rounded-lg p-4"},ie={class:"flex items-center justify-between mb-4"},ue={class:"text-sm font-medium text-gray-700"},de={class:"max-h-60 overflow-y-auto space-y-4"},me={class:"px-3 py-2 bg-white border-b border-gray-200 rounded-t-lg"},ce={class:"flex items-center"},pe=["id","checked","onChange"],be=["for"],fe={class:"px-3 py-2 space-y-2"},ge=["id","value"],ve=["for"],ye=["value"],xe={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200"},he=["disabled"],Ee=L({__name:"EditUser",setup(we){const p=Q(),V=K(),f=d(!1),U=d(!0),h=d([]),w=d([]),C=d([]),i=d(null),r=d({user_id:"",username:"",email_address:"",msisdn:"",role_id:"",permissions:[],client_id:"",status:1}),g=S(()=>h.value.find(t=>t.role_id===parseInt(r.value.role_id))),M=S(()=>{const t={};return w.value.forEach(e=>{const o=e.module||"general";t[o]||(t[o]=[]),t[o].push(e)}),t}),R=async()=>{var e,o;const t=V.params.id;if(!t){p.push({name:"system-roles"});return}try{const l=await x.getUsers({limit:1e3});if(l.status===200){const u=((e=l.message)==null?void 0:e.data)||[];i.value=u.find(b=>b.user_id===t)||null,i.value?r.value={user_id:i.value.user_id,username:i.value.username,email_address:i.value.email_address,msisdn:i.value.msisdn,role_id:i.value.role_id.toString(),permissions:((o=i.value.permissions)==null?void 0:o.map(b=>b.id))||[],client_id:i.value.client_id||"",status:i.value.status}:p.push({name:"system-users"})}}catch(l){console.error("Error fetching user:",l),p.push({name:"system-users"})}finally{U.value=!1}},N=async()=>{var t;try{const e=await x.getRoles({limit:100});e.status===200&&(h.value=((t=e.message)==null?void 0:t.data)||[])}catch(e){console.error("Error fetching roles:",e)}},B=async()=>{var t;try{const e=await x.getPermissions({limit:100});e.status===200&&(w.value=((t=e.message)==null?void 0:t.data)||[])}catch(e){console.error("Error fetching permissions:",e)}},q=async()=>{var t;try{const e=await X.getClients({limit:100});e.status===200&&(C.value=((t=e.message)==null?void 0:t.data)||[])}catch(e){console.error("Error fetching clients:",e)}},I=()=>{var t;g.value&&(r.value.permissions=((t=g.value.permissions)==null?void 0:t.map(e=>e.id))||[])},E=t=>t.every(e=>r.value.permissions.includes(e.id)),$=t=>{E(t)?t.forEach(o=>{const l=r.value.permissions.indexOf(o.id);l>-1&&r.value.permissions.splice(l,1)}):t.forEach(o=>{r.value.permissions.includes(o.id)||r.value.permissions.push(o.id)})},j=()=>{r.value.permissions=w.value.map(t=>t.id)},F=()=>{r.value.permissions=[]},P=async()=>{f.value=!0;try{const t=await x.updateUser({user_id:r.value.user_id,username:r.value.username,email_address:r.value.email_address,msisdn:r.value.msisdn,role_id:parseInt(r.value.role_id),permissions:r.value.permissions,status:r.value.status});t.status===200?p.push({name:"system-roles"}):console.error("Failed to update user:",t.message)}catch(t){console.error("Error updating user:",t)}finally{f.value=!1}},A=()=>{p.push({name:"system-roles"})},D=t=>t.charAt(0).toUpperCase()+t.slice(1).replace(/[_-]/g," ");return O(async()=>{await Promise.all([N(),B(),q()]),await R()}),(t,e)=>(a(),n("div",Z,[s("div",ee,[s("div",se,[e[8]||(e[8]=s("div",null,[s("h1",{class:"text-2xl font-bold text-gray-900"},"Edit User"),s("p",{class:"text-gray-600 mt-1"},"Update user information, roles and permissions")],-1)),s("button",{onClick:A,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"},[T(G(Y),{class:"w-4 h-4 mr-2"}),e[7]||(e[7]=z(" Back to Users "))])])]),U.value?(a(),n("div",te,e[9]||(e[9]=[s("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"},null,-1),s("p",{class:"mt-2 text-sm text-gray-500"},"Loading user data...",-1)]))):(a(),n("div",oe,[s("form",{onSubmit:J(P,["prevent"]),class:"space-y-6"},[s("div",null,[e[15]||(e[15]=s("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Basic Information",-1)),s("div",re,[s("div",null,[e[10]||(e[10]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Username *",-1)),m(s("input",{"onUpdate:modelValue":e[0]||(e[0]=o=>r.value.username=o),type:"text",required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter username"},null,512),[[_,r.value.username]])]),s("div",null,[e[11]||(e[11]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Email Address *",-1)),m(s("input",{"onUpdate:modelValue":e[1]||(e[1]=o=>r.value.email_address=o),type:"email",required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter email address"},null,512),[[_,r.value.email_address]])]),s("div",null,[e[12]||(e[12]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Phone Number *",-1)),m(s("input",{"onUpdate:modelValue":e[2]||(e[2]=o=>r.value.msisdn=o),type:"tel",required:"",class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter phone number"},null,512),[[_,r.value.msisdn]])]),s("div",null,[e[14]||(e[14]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Status",-1)),m(s("select",{"onUpdate:modelValue":e[3]||(e[3]=o=>r.value.status=o),class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},e[13]||(e[13]=[s("option",{value:1},"Active",-1),s("option",{value:0},"Inactive",-1)]),512),[[k,r.value.status]])])])]),s("div",null,[e[18]||(e[18]=s("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Role Assignment",-1)),s("div",null,[e[17]||(e[17]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Select Role *",-1)),m(s("select",{"onUpdate:modelValue":e[4]||(e[4]=o=>r.value.role_id=o),required:"",onChange:I,class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},[e[16]||(e[16]=s("option",{value:""},"Select a role",-1)),(a(!0),n(v,null,y(h.value,o=>(a(),n("option",{key:o.role_id,value:o.role_id},c(o.role_name),9,le))),128))],544),[[k,r.value.role_id]]),g.value?(a(),n("p",ne,c(g.value.description),1)):H("",!0)])]),s("div",null,[e[19]||(e[19]=s("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Permissions",-1)),s("div",ae,[s("div",ie,[s("span",ue,c(r.value.permissions.length)+" permissions selected ",1),s("div",{class:"flex space-x-2"},[s("button",{type:"button",onClick:j,class:"text-sm text-blue-600 hover:text-blue-800"}," Select All "),s("button",{type:"button",onClick:F,class:"text-sm text-gray-600 hover:text-gray-800"}," Clear All ")])]),s("div",de,[(a(!0),n(v,null,y(M.value,(o,l)=>(a(),n("div",{key:l,class:"border border-gray-200 rounded-lg"},[s("div",me,[s("div",ce,[s("input",{id:`module-${l}`,type:"checkbox",checked:E(o),onChange:u=>$(o),class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,40,pe),s("label",{for:`module-${l}`,class:"ml-2 text-sm font-medium text-gray-900 capitalize"},c(D(l)),9,be)])]),s("div",fe,[(a(!0),n(v,null,y(o,u=>(a(),n("div",{key:u.id,class:"flex items-center"},[m(s("input",{id:`permission-${u.id}`,"onUpdate:modelValue":e[5]||(e[5]=b=>r.value.permissions=b),value:u.id,type:"checkbox",class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,8,ge),[[W,r.value.permissions]]),s("label",{for:`permission-${u.id}`,class:"ml-2 text-sm text-gray-700"},c(u.name),9,ve)]))),128))])]))),128))])])]),s("div",null,[e[22]||(e[22]=s("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Client Assignment",-1)),s("div",null,[e[21]||(e[21]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Assign Client (Optional)",-1)),m(s("select",{"onUpdate:modelValue":e[6]||(e[6]=o=>r.value.client_id=o),class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},[e[20]||(e[20]=s("option",{value:""},"No client assigned",-1)),(a(!0),n(v,null,y(C.value,o=>(a(),n("option",{key:o.client_id,value:o.client_id},c(o.client_name),9,ye))),128))],512),[[k,r.value.client_id]])])]),s("div",xe,[s("button",{type:"button",onClick:A,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Cancel "),s("button",{type:"submit",disabled:f.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},c(f.value?"Updating...":"Update User"),9,he)])],32)]))]))}});export{Ee as default};
