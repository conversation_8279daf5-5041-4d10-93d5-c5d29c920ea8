import{c as r,b as s,o as n,d as S,a as j,r as M,x as T,w as U,j as f,h as u,y as m,v as h,f as i,C as p,F as b,m as g,i as F,z as D,J as R,k as I,T as N,t as v,I as $}from"./index-DOaBqVmr.js";import{r as E}from"./MagnifyingGlassIcon-D7MUVaIi.js";import{r as z}from"./XMarkIcon-CqcEu60T.js";function H(y,w){return n(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"})])}const P={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6"},J={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4"},L={class:"flex-1 max-w-md"},q={class:"relative"},G={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},K=["placeholder"],Q={class:"flex items-center space-x-2"},W={key:0,class:"relative"},X=["value"],Y={key:1,class:"relative"},Z=["value"],ee={key:2,class:"relative"},te=["value"],oe={key:0,class:"mt-4 pt-4 border-t border-gray-200 overflow-hidden"},se={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},le={key:0},re={class:"grid grid-cols-2 gap-2"},ne={key:1},ae=["value"],ce=S({__name:"SearchFilter",props:{filters:{default:()=>({})},searchPlaceholder:{default:"Search..."},showStatusFilter:{type:Boolean,default:!0},showRoleFilter:{type:Boolean,default:!1},showModuleFilter:{type:Boolean,default:!1},showDateFilter:{type:Boolean,default:!1},showClientFilter:{type:Boolean,default:!1},hasAdvancedFilters:{type:Boolean,default:!1},statusOptions:{default:()=>[{value:"1",label:"Active"},{value:"0",label:"Inactive"}]},roleOptions:{default:()=>[]},moduleOptions:{default:()=>[]},clientOptions:{default:()=>[]}},emits:["update:filters","search","filter","clear"],setup(y,{emit:w}){const x=y,d=w,o=j({...x.filters}),c=M(!1);let k;const C=T(()=>Object.values(o).some(l=>l!==""&&l!==null&&l!==void 0)),_=()=>{clearTimeout(k),k=setTimeout(()=>{d("search",o.search||""),d("update:filters",{...o})},300)},a=()=>{d("filter",{...o}),d("update:filters",{...o})},A=()=>{c.value=!c.value},O=()=>{Object.keys(o).forEach(l=>{o[l]=""}),d("clear"),d("update:filters",{...o})},V=()=>{const l=["search","status","role_id","module"];Object.keys(o).forEach(e=>{l.includes(e)||(o[e]="")}),a()},B=()=>{d("filter",{...o}),c.value=!1};return U(()=>x.filters,l=>{Object.assign(o,l)},{deep:!0}),(l,e)=>(n(),r("div",P,[s("div",J,[s("div",L,[s("div",q,[s("div",G,[f(m(E),{class:"h-5 w-5 text-gray-400"})]),u(s("input",{"onUpdate:modelValue":e[0]||(e[0]=t=>o.search=t),type:"text",placeholder:l.searchPlaceholder,class:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm",onInput:_},null,40,K),[[h,o.search]])])]),s("div",Q,[l.showStatusFilter?(n(),r("div",W,[u(s("select",{"onUpdate:modelValue":e[1]||(e[1]=t=>o.status=t),onChange:a,class:"block w-32 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"},[e[7]||(e[7]=s("option",{value:""},"All Status",-1)),(n(!0),r(b,null,g(l.statusOptions,t=>(n(),r("option",{key:t.value,value:t.value},v(t.label),9,X))),128))],544),[[p,o.status]])])):i("",!0),l.showRoleFilter?(n(),r("div",Y,[u(s("select",{"onUpdate:modelValue":e[2]||(e[2]=t=>o.role_id=t),onChange:a,class:"block w-40 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"},[e[8]||(e[8]=s("option",{value:""},"All Roles",-1)),(n(!0),r(b,null,g(l.roleOptions,t=>(n(),r("option",{key:t.role_id,value:t.role_id},v(t.role_name),9,Z))),128))],544),[[p,o.role_id]])])):i("",!0),l.showModuleFilter?(n(),r("div",ee,[u(s("select",{"onUpdate:modelValue":e[3]||(e[3]=t=>o.module=t),onChange:a,class:"block w-40 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"},[e[9]||(e[9]=s("option",{value:""},"All Modules",-1)),(n(!0),r(b,null,g(l.moduleOptions,t=>(n(),r("option",{key:t.value,value:t.value},v(t.label),9,te))),128))],544),[[p,o.module]])])):i("",!0),l.hasAdvancedFilters?(n(),r("button",{key:3,onClick:A,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[f(m(H),{class:"h-4 w-4 mr-2"}),e[10]||(e[10]=F(" Filters ")),f(m(R),{class:D(["h-4 w-4 ml-1 transition-transform duration-200",{"rotate-180":c.value}])},null,8,["class"])])):i("",!0),C.value?(n(),r("button",{key:4,onClick:O,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},[f(m(z),{class:"h-4 w-4 mr-2"}),e[11]||(e[11]=F(" Clear "))])):i("",!0)])]),f(N,{"enter-active-class":"transition-all duration-300 ease-out","enter-from-class":"opacity-0 max-h-0","enter-to-class":"opacity-100 max-h-96","leave-active-class":"transition-all duration-300 ease-in","leave-from-class":"opacity-100 max-h-96","leave-to-class":"opacity-0 max-h-0"},{default:I(()=>[c.value&&l.hasAdvancedFilters?(n(),r("div",oe,[s("div",se,[l.showDateFilter?(n(),r("div",le,[e[12]||(e[12]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Date Range",-1)),s("div",re,[u(s("input",{"onUpdate:modelValue":e[4]||(e[4]=t=>o.start_date=t),type:"date",onChange:a,class:"block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"},null,544),[[h,o.start_date]]),u(s("input",{"onUpdate:modelValue":e[5]||(e[5]=t=>o.end_date=t),type:"date",onChange:a,class:"block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"},null,544),[[h,o.end_date]])])])):i("",!0),l.showClientFilter?(n(),r("div",ne,[e[14]||(e[14]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Client",-1)),u(s("select",{"onUpdate:modelValue":e[6]||(e[6]=t=>o.client_id=t),onChange:a,class:"block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"},[e[13]||(e[13]=s("option",{value:""},"All Clients",-1)),(n(!0),r(b,null,g(l.clientOptions,t=>(n(),r("option",{key:t.value,value:t.value},v(t.label),9,ae))),128))],544),[[p,o.client_id]])])):i("",!0),$(l.$slots,"advanced-filters",{filters:o,handleChange:a})]),s("div",{class:"flex items-center justify-end space-x-3 mt-4"},[s("button",{onClick:V,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Reset "),s("button",{onClick:B,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Apply Filters ")])])):i("",!0)]),_:3})]))}});export{ce as _};
