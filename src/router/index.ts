import { createRouter, createWebHistory } from 'vue-router'
import DashboardLayout from '@/layouts/DashboardLayout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // Authentication routes
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/Auth/Login.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/forgot-password',
      name: 'forgot-password',
      component: () => import('@/views/Auth/ForgotPassword.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/reset-password',
      name: 'reset-password',
      component: () => import('@/views/Auth/ResetPassword.vue'),
      meta: { requiresGuest: true }
    },
    // Dashboard routes
    {
      path: '/',
      component: DashboardLayout,
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'dashboard',
          component: () => import('@/views/Dashboard.vue')
        },
        // Organizations routes
        {
          path: '/organizations',
          name: 'organisations',
          component: () => import('@/views/Organizations/OrganizationsList.vue')
        },
        {
          path: '/organizations/config',
          name: 'organisations-config',
          component: () => import('@/views/Organizations/OrganizationsConfig.vue')
        },
        {
          path: '/organizations/bulk',
          name: 'organisations-bulk',
          component: () => import('@/views/Organizations/BulkSMS.vue')
        },
       
        // Organizations routes (redirected to clients)
        {
          path: '/organisations',
          redirect: '/clients'
        },
        {
          path: '/organisations/add',
          redirect: '/clients/add'
        },
        {
          path: '/organisations/config',
          redirect: '/clients/config'
        },
        {
          path: '/organisations/bulk-sms',
          redirect: '/clients/bulk'
        },
        // Legacy organizations route
        {
          path: '/organizations',
          redirect: '/clients'
        },
        // Merchants routes
        {
          path: '/merchants',
          name: 'merchants',
          component: () => import('@/views/Merchants/MerchantsList.vue')
        },
        {
          path: '/merchants/config',
          name: 'merchants-config',
          component: () => import('@/views/Merchants/MerchantsConfig.vue')
        },
        {
          path: '/merchants/bulk',
          name: 'merchants-bulk',
          component: () => import('@/views/Merchants/MerchantsBulkSMS.vue')
        },
        // Loans routes
        {
          path: '/loans/requests',
          name: 'requests',
          component: () => import('@/views/Loans/LoanRequests.vue')
        },
        {
          path: '/loans/limits',
          name: 'limits',
          component: () => import('@/views/Loans/LoanLimits.vue')
        },
        {
          path: '/loans/check-off',
          name: 'check-off',
          component: () => import('@/views/Loans/CheckOff.vue')
        },
        {
          path: '/loans/accounts',
          name: 'loan-accounts',
          component: () => import('@/views/Loans/LoanAccounts.vue')
        },
        {
          path: '/loans/products',
          name: 'loan-products',
          component: () => import('@/views/Loans/LoanProducts.vue')
        },
        // TODO: Add these routes when the components are created
        // {
        //   path: '/loans/products/add',
        //   name: 'loan-products-add',
        //   component: () => import('@/views/Loans/Products/ProductsAdd.vue')
        // },
        // {
        //   path: '/loans/products/edit/:id',
        //   name: 'loan-products-edit',
        //   component: () => import('@/views/Loans/Products/ProductsEdit.vue')
        // },
        {
          path: '/loans/repayments',
          name: 'loan-repayments',
          component: () => import('@/views/Loans/LoanRepayments.vue')
        },
        // Financial Operations routes
        {
          path: '/transactions',
          name: 'transactions',
          component: () => import('@/views/Financial/Transactions.vue')
        },
        {
          path: '/withdrawals',
          name: 'withdrawals',
          component: () => import('@/views/Financial/Withdrawals.vue')
        },
        {
          path: '/bill-payments',
          name: 'bill-payments',
          component: () => import('@/views/Financial/BillPayments.vue')
        },
        {
          path: '/bill-payments/add',
          name: 'bill-payments-add',
          component: () => import('@/views/Financial/AddBillPayment.vue')
        },
        // Customer Management routes
        {
          path: '/customers',
          name: 'customers',
          component: () => import('@/views/Customers/CustomerSearch.vue')
        },
        // Partners routes
        {
          path: '/partners',
          name: 'partners',
          component: () => import('@/views/Partners/Partners.vue')
        },
        {
          path: '/partners/services',
          name: 'partner-services',
          component: () => import('@/views/Partners/PartnerServices.vue')
        },
        {
          path: '/partners/bets',
          name: 'partners-bets',
          component: () => import('@/views/Partners/PartnersBets.vue')
        },
        {
          path: '/partners/bet-slips',
          name: 'partners-bet-slips',
          component: () => import('@/views/Partners/PartnersBetSlips.vue')
        },
        // Services routes
        {
          path: '/services',
          name: 'services',
          component: () => import('@/views/Services/Services.vue')
        },
        // System Administration routes
        {
          path: '/system/users',
          name: 'system-users',
          component: () => import('@/views/System/SystemUsers.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/users/add',
          name: 'add-user',
          component: () => import('@/views/System/AddUser.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/users/edit/:id',
          name: 'edit-user',
          component: () => import('@/views/System/EditUser.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/roles',
          name: 'system-roles',
          component: () => import('@/views/System/SystemRoles.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/roles/add',
          name: 'add-role',
          component: () => import('@/views/System/AddRole.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/roles/edit/:id',
          name: 'edit-role',
          component: () => import('@/views/System/EditRole.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/permissions',
          name: 'system-permissions',
          component: () => import('@/views/System/SystemPermissions.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/permissions/add',
          name: 'add-permission',
          component: () => import('@/views/System/AddPermission.vue'),
          meta: { requiresAuth: true }
        },
        {
          path: '/system/permissions/edit/:id',
          name: 'edit-permission',
          component: () => import('@/views/System/EditPermission.vue')
        },
        // Debug route (development only)
        {
          path: '/debug',
          name: 'debug',
          component: () => import('@/views/Debug.vue'),
          meta: { requiresAuth: false }
        }
      ]
    }
  ]
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  // Import auth store dynamically to avoid circular dependency
  const { useAuthStore } = await import('@/stores/auth')
  const authStore = useAuthStore()

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest)

  if (requiresAuth && !authStore.isAuthenticated) {
    // Redirect to login if authentication is required but user is not authenticated
    next({ name: 'login' })
  } else if (requiresGuest && authStore.isAuthenticated) {
    // Redirect to dashboard if guest route but user is authenticated
    next({ name: 'dashboard' })
  } else {
    // Proceed to route
    next()
  }
})

export default router
export { router }
